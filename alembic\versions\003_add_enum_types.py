"""Add enum types for roles and status fields

Revision ID: 003_add_enum_types
Revises: 002_add_editor_generative_tables
Create Date: 2025-08-06 12:10:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import ENUM

# revision identifiers, used by Alembic.
revision = '003_add_enum_types'
down_revision = '002_add_editor_generative_tables'
branch_labels = None
depends_on = None

def upgrade():
    # Create enum types
    user_role_enum = ENUM('SUPER_ADMIN', 'ADMIN', 'USER', 'VIEWER', name='userrole')
    project_status_enum = ENUM('INITIAL', 'EN_COURS', 'EN_PAUSE', 'TERMINE', 'ANNULE', name='projectstatus')
    project_nature_enum = ENUM('DEVIS', 'AO', 'AFFAIRE', name='projectnature')
    lot_phase_enum = ENUM('ESQ', 'APD', 'PRODCE', 'EXE', name='lotphase')
    document_status_enum = ENUM('BROUILLON', 'REVIEW', 'APPROVED', 'REJECTED', 'ARCHIVED', name='documentstatus')
    
    # Create enum types in PostgreSQL
    user_role_enum.create(op.get_bind())
    project_status_enum.create(op.get_bind())
    project_nature_enum.create(op.get_bind())
    lot_phase_enum.create(op.get_bind())
    document_status_enum.create(op.get_bind())
    
    # Update columns to use enum types
    op.alter_column('workspace_members', 'role',
                    type_=sa.Enum('SUPER_ADMIN', 'ADMIN', 'USER', 'VIEWER', name='userrole'),
                    postgresql_using='role::userrole')
    
    op.alter_column('projects', 'status',
                    type_=sa.Enum('INITIAL', 'EN_COURS', 'EN_PAUSE', 'TERMINE', 'ANNULE', name='projectstatus'),
                    postgresql_using='status::projectstatus')
    
    op.alter_column('projects', 'nature',
                    type_=sa.Enum('DEVIS', 'AO', 'AFFAIRE', name='projectnature'),
                    postgresql_using='nature::projectnature')
    
    op.alter_column('lots', 'phase',
                    type_=sa.Enum('ESQ', 'APD', 'PRODCE', 'EXE', name='lotphase'),
                    postgresql_using='phase::lotphase')
    
    op.alter_column('technical_documents', 'status',
                    type_=sa.Enum('BROUILLON', 'REVIEW', 'APPROVED', 'REJECTED', 'ARCHIVED', name='documentstatus'),
                    postgresql_using='status::documentstatus')

def downgrade():
    # Revert columns back to string
    op.alter_column('workspace_members', 'role', type_=sa.String())
    op.alter_column('projects', 'status', type_=sa.String())
    op.alter_column('projects', 'nature', type_=sa.String())
    op.alter_column('lots', 'phase', type_=sa.String())
    op.alter_column('technical_documents', 'status', type_=sa.String())
    
    # Drop enum types
    op.execute("DROP TYPE IF EXISTS userrole")
    op.execute("DROP TYPE IF EXISTS projectstatus")
    op.execute("DROP TYPE IF EXISTS projectnature")
    op.execute("DROP TYPE IF EXISTS lotphase")
    op.execute("DROP TYPE IF EXISTS documentstatus")