# app/api/v1/endpoints/tcompanies.py
from fastapi import APIRouter, Depends, HTTPException, status, Query, UploadFile, File
from sqlalchemy.orm import Session
from typing import List, Optional
from app.db.session import get_db
from app.models.tcompany import TCompany
from app.schemas.tcompany import TCompanyCreate, TCompanyUpdate, TCompanyResponse
from app.middleware.auth_middleware import get_current_user

router = APIRouter()

@router.post("/", response_model=TCompanyResponse)
async def create_tcompany(
    tcompany: TCompanyCreate,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Créer une nouvelle entreprise tierce"""
    from app.models.rbac import WorkspaceUserRole
    
    # Vérifier l'accès au workspace
    access = db.query(WorkspaceUserRole).filter(
        WorkspaceUserRole.workspace_id == tcompany.workspace_id,
        WorkspaceUserRole.user_id == current_user["id"]
    ).first()
    
    if not access:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this workspace"
        )
    
    db_tcompany = TCompany(**tcompany.dict())
    db.add(db_tcompany)
    db.commit()
    db.refresh(db_tcompany)
    
    return db_tcompany

@router.get("/", response_model=List[TCompanyResponse])
async def get_tcompanies(
    workspace_id: int = Query(..., description="ID du workspace"),
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Récupérer toutes les entreprises tierces d'un workspace"""
    from app.models.rbac import WorkspaceUserRole
    
    # Vérifier l'accès au workspace
    access = db.query(WorkspaceUserRole).filter(
        WorkspaceUserRole.workspace_id == workspace_id,
        WorkspaceUserRole.user_id == current_user["id"]
    ).first()
    
    if not access:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this workspace"
        )
    
    tcompanies = db.query(TCompany).filter(
        TCompany.workspace_id == workspace_id,
        TCompany.is_active == True
    ).all()
    
    return tcompanies

@router.get("/{tcompany_id}", response_model=TCompanyResponse)
async def get_tcompany(
    tcompany_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Récupérer une entreprise tierce spécifique"""
    from app.models.rbac import WorkspaceUserRole
    
    tcompany = db.query(TCompany).join(
        WorkspaceUserRole,
        TCompany.workspace_id == WorkspaceUserRole.workspace_id
    ).filter(
        TCompany.id == tcompany_id,
        WorkspaceUserRole.user_id == current_user["id"],
        TCompany.is_active == True
    ).first()
    
    if not tcompany:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="TCompany not found"
        )
    
    return tcompany

@router.put("/{tcompany_id}", response_model=TCompanyResponse)
async def update_tcompany(
    tcompany_id: int,
    tcompany: TCompanyUpdate,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Mettre à jour une entreprise tierce"""
    from app.models.rbac import WorkspaceUserRole
    
    db_tcompany = db.query(TCompany).join(
        WorkspaceUserRole,
        TCompany.workspace_id == WorkspaceUserRole.workspace_id
    ).filter(
        TCompany.id == tcompany_id,
        WorkspaceUserRole.user_id == current_user["id"]
    ).first()
    
    if not db_tcompany:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="TCompany not found"
        )
    
    for field, value in tcompany.dict(exclude_unset=True).items():
        setattr(db_tcompany, field, value)
    
    db.commit()
    db.refresh(db_tcompany)
    
    return db_tcompany

@router.delete("/{tcompany_id}")
async def delete_tcompany(
    tcompany_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Supprimer une entreprise tierce (soft delete)"""
    from app.models.rbac import WorkspaceUserRole
    
    db_tcompany = db.query(TCompany).join(
        WorkspaceUserRole,
        TCompany.workspace_id == WorkspaceUserRole.workspace_id
    ).filter(
        TCompany.id == tcompany_id,
        WorkspaceUserRole.user_id == current_user["id"]
    ).first()
    
    if not db_tcompany:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="TCompany not found"
        )
    
    db_tcompany.is_active = False
    db.commit()
    
    return {"message": "TCompany deleted successfully"}

@router.post("/{tcompany_id}/upload-logo")
async def upload_logo(
    tcompany_id: int,
    file: UploadFile = File(...),
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Uploader un logo pour une entreprise tierce"""
    from app.models.rbac import WorkspaceUserRole
    
    db_tcompany = db.query(TCompany).join(
        WorkspaceUserRole,
        TCompany.workspace_id == WorkspaceUserRole.workspace_id
    ).filter(
        TCompany.id == tcompany_id,
        WorkspaceUserRole.user_id == current_user["id"]
    ).first()
    
    if not db_tcompany:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="TCompany not found"
        )
    
    # Ici vous implémenteriez le stockage du fichier
    # Pour l'instant, on retourne une URL simulée
    logo_url = f"/uploads/tcompanies/{tcompany_id}/logo.png"
    db_tcompany.logo_url = logo_url
    db.commit()
    
    return {"logo_url": logo_url}

@router.delete("/{tcompany_id}/logo")
async def delete_logo(
    tcompany_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Supprimer le logo d'une entreprise tierce"""
    from app.models.rbac import WorkspaceUserRole
    
    db_tcompany = db.query(TCompany).join(
        WorkspaceUserRole,
        TCompany.workspace_id == WorkspaceUserRole.workspace_id
    ).filter(
        TCompany.id == tcompany_id,
        WorkspaceUserRole.user_id == current_user["id"]
    ).first()
    
    if not db_tcompany:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="TCompany not found"
        )
    
    db_tcompany.logo_url = None
    db.commit()
    
    return {"message": "Logo deleted successfully"}
