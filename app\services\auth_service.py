# app/services/auth_service.py
import redis
import json
from typing import Optional, List, Dict
from supabase import create_client, Client
from sqlalchemy.orm import Session
from app.core.config import settings

class AuthService:
    def __init__(self):
        self.supabase: Client = create_client(
            settings.SUPABASE_URL, 
            settings.SUPABASE_SERVICE_ROLE_KEY
        )
        self.redis_client = redis.from_url(settings.REDIS_URL)
        self.cache_ttl = settings.REDIS_TTL

    async def verify_token(self, token: str) -> Optional[Dict]:
        """Vérification du token Supabase JWT"""
        try:
            user = self.supabase.auth.get_user(token)
            return user.user.model_dump() if user.user else None
        except Exception as e:
            print(f"Token verification failed: {e}")
            return None

    async def get_user_permissions(self, user_id: str, workspace_id: int, db: Session) -> List[str]:
        """Récupération des permissions utilisateur avec cache Redis"""
        cache_key = f"permissions:{user_id}:{workspace_id}"
        
        # 1. Check cache first
        cached = self.redis_client.get(cache_key)
        if cached:
            return json.loads(cached)
        
        # 2. Query database si pas en cache
        permissions = self._fetch_user_permissions_from_db(user_id, workspace_id, db)
        
        # 3. Cache result
        self.redis_client.setex(
            cache_key, 
            self.cache_ttl, 
            json.dumps(permissions)
        )
        
        return permissions

    def _fetch_user_permissions_from_db(self, user_id: str, workspace_id: int, db: Session) -> List[str]:
        """Query direct en base pour récupérer les permissions"""
        from app.models.rbac import WorkspaceUserRole, WorkspaceRolePermission, Permission
        
        query = db.query(Permission.name).join(
            WorkspaceRolePermission,
            Permission.id == WorkspaceRolePermission.permission_id
        ).join(
            WorkspaceUserRole,
            (WorkspaceUserRole.workspace_id == WorkspaceRolePermission.workspace_id) &
            (WorkspaceUserRole.role_name == WorkspaceRolePermission.role_name)
        ).filter(
            WorkspaceUserRole.user_id == user_id,
            WorkspaceUserRole.workspace_id == workspace_id
        ).distinct()
        
        return [row[0] for row in query.all()]

    async def check_permission(self, user_id: str, workspace_id: int, permission: str, db: Session) -> bool:
        """Vérification rapide d'une permission spécifique"""
        permissions = await self.get_user_permissions(user_id, workspace_id, db)
        return permission in permissions

    async def get_user_workspaces(self, user_id: str, db: Session) -> List[Dict]:
        """Récupérer tous les workspaces d'un utilisateur"""
        from app.models.rbac import WorkspaceUserRole
        from app.models.workspace import Workspace
        
        query = db.query(Workspace, WorkspaceUserRole.role_name).join(
            WorkspaceUserRole,
            Workspace.id == WorkspaceUserRole.workspace_id
        ).filter(
            WorkspaceUserRole.user_id == user_id,
            Workspace.is_active == True
        )
        
        return [
            {
                "workspace": workspace,
                "role": role_name
            }
            for workspace, role_name in query.all()
        ]

    async def invalidate_user_cache(self, user_id: str, workspace_id: int = None):
        """Invalidation du cache utilisateur"""
        if workspace_id:
            cache_key = f"permissions:{user_id}:{workspace_id}"
            self.redis_client.delete(cache_key)
        else:
            # Invalider tous les workspaces de l'utilisateur
            pattern = f"permissions:{user_id}:*"
            keys = self.redis_client.keys(pattern)
            if keys:
                self.redis_client.delete(*keys)

    async def get_user_by_id(self, user_id: str) -> Optional[Dict]:
        """Récupérer un utilisateur par ID depuis Supabase"""
        try:
            response = self.supabase.auth.admin.get_user_by_id(user_id)
            return response.user.model_dump() if response.user else None
        except Exception as e:
            print(f"Error getting user: {e}")
            return None

# Instance globale
auth_service = AuthService()
