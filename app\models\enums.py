from enum import Enum

class UserRole(str, Enum):
    """User roles for workspace membership"""
    SUPER_ADMIN = "SUPER_ADMIN"
    ADMIN = "ADMIN"
    USER = "USER"
    VIEWER = "VIEWER"

class ProjectStatus(str, Enum):
    """Project status values"""
    PLANNING = "INITIAL"
    ACTIVE = "EN_COURS"
    ON_HOLD = "EN_PAUSE"
    COMPLETED = "TERMINE"
    CANCELLED = "ANNULE"

class ProjectNature(str, Enum):
    """Project nature values"""
    DEVIS = "DEVIS"
    AO = "AO"
    AFFAIRE = "AFFAIRE"

class LotPhase(str, Enum):
    """Lot phase values"""
    ESQ = "ESQ"
    APD = "APD"
    PRODCE = "PRODCE"
    EXE = "EXE"

class DocumentStatus(str, Enum):
    """Document status values"""
    DRAFT = "BROUILLON"
    REVIEW = "REVIEW"
    APPROVED = "APPROVED"
    REJECTED = "REJECTED"
    ARCHIVED = "ARCHIVED"

class StakeholderRole(str, Enum):
    """Stakeholder roles in construction projects"""
    MOA = "MOA"  # Maître d'ouvrage
    MOADEL = "MOADEL"  # Maître d'ouvrage délégué
    ARCHI = "ARCHI"  # Architecte
    BE = "BE"  # Bureau d'études
    BC = "BC"  # Bureau de contrôle
    OPC = "OPC"  # Opération de coordination
    ENT = "ENT"  # Entreprise
    FO = "FO"  # Fournisseur