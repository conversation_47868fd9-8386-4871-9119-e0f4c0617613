#!/usr/bin/env python3
"""
Comprehensive seed script for ORBIS Backend V2 test data
Creates:
- Super admin user (<EMAIL> / orbis123!)
- Workspace admin user (<EMAIL> / orbis123!)
- Test workspace
- Test project
- Test lot
- Complete RBAC setup
"""

import asyncio
import os
import sys
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import create_engine
from app.db.session import SessionLocal
from app.models.user import User
from app.models.workspace import Workspace, WorkspaceMember, WorkspaceSettings
from app.models.project import Project
from app.models.lot import Lot
from app.models.rbac import Role, Permission, role_permissions
from app.models.tcompany import TCompany
from app.core.config import settings

# Test data configuration
TEST_USERS = [
    {
        "email": "<EMAIL>",
        "password": "orbis123!",
        "first_name": "Super",
        "last_name": "Admin",
        "role": "super_admin"
    },
    {
        "email": "<EMAIL>",
        "password": "orbis123!",
        "first_name": "Test",
        "last_name": "User",
        "role": "workspace_admin"
    }
]

TEST_WORKSPACE = {
    "name": "Test Workspace",
    "slug": "test-workspace",
    "description": "Test workspace for development and testing"
}

TEST_PROJECT = {
    "name": "Test Construction Project",
    "description": "A test project for demonstrating ORBIS features",
    "status": "planning",
    "nature": "residential",
    "start_date": datetime.now().date(),
    "end_date": (datetime.now() + timedelta(days=365)).date(),
    "address": "123 Test Street, Test City",
    "client_name": "Test Client",
    "budget": 500000.00
}

TEST_LOT = {
    "name": "Test Lot 1 - Structural Work",
    "description": "Foundation and structural framework",
    "phase": "structural",
    "start_date": datetime.now().date(),
    "end_date": (datetime.now() + timedelta(days=120)).date(),
    "budget": 150000.00
}

TEST_COMPANY = {
    "name": "Test Construction Company",
    "siret": "12345678901234",
    "address": "456 Builder Ave, Construction City",
    "phone": "+33 1 23 45 67 89",
    "email": "<EMAIL>",
    "website": "https://test-construction.fr"
}

def create_super_admin_role(session: Session, workspace_id: int):
    """Create super admin role with all permissions"""
    super_admin_role = Role(
        workspace_id=workspace_id,
        name="Super Admin",
        description="Full system administrator with all permissions",
        is_active=True
    )
    session.add(super_admin_role)
    session.flush()
    
    # Create all permissions
    permissions = [
        # User management
        {"name": "users.create", "description": "Create users", "resource": "users", "action": "create"},
        {"name": "users.read", "description": "Read users", "resource": "users", "action": "read"},
        {"name": "users.update", "description": "Update users", "resource": "users", "action": "update"},
        {"name": "users.delete", "description": "Delete users", "resource": "users", "action": "delete"},
        
        # Workspace management
        {"name": "workspaces.create", "description": "Create workspaces", "resource": "workspaces", "action": "create"},
        {"name": "workspaces.read", "description": "Read workspaces", "resource": "workspaces", "action": "read"},
        {"name": "workspaces.update", "description": "Update workspaces", "resource": "workspaces", "action": "update"},
        {"name": "workspaces.delete", "description": "Delete workspaces", "resource": "workspaces", "action": "delete"},
        
        # Project management
        {"name": "projects.create", "description": "Create projects", "resource": "projects", "action": "create"},
        {"name": "projects.read", "description": "Read projects", "resource": "projects", "action": "read"},
        {"name": "projects.update", "description": "Update projects", "resource": "projects", "action": "update"},
        {"name": "projects.delete", "description": "Delete projects", "resource": "projects", "action": "delete"},
        
        # Lot management
        {"name": "lots.create", "description": "Create lots", "resource": "lots", "action": "create"},
        {"name": "lots.read", "description": "Read lots", "resource": "lots", "action": "read"},
        {"name": "lots.update", "description": "Update lots", "resource": "lots", "action": "update"},
        {"name": "lots.delete", "description": "Delete lots", "resource": "lots", "action": "delete"},
        
        # Document management
        {"name": "documents.create", "description": "Create documents", "resource": "documents", "action": "create"},
        {"name": "documents.read", "description": "Read documents", "resource": "documents", "action": "read"},
        {"name": "documents.update", "description": "Update documents", "resource": "documents", "action": "update"},
        {"name": "documents.delete", "description": "Delete documents", "resource": "documents", "action": "delete"},
        
        # Technical document management
        {"name": "technical_documents.create", "description": "Create technical documents", "resource": "technical_documents", "action": "create"},
        {"name": "technical_documents.read", "description": "Read technical documents", "resource": "technical_documents", "action": "read"},
        {"name": "technical_documents.update", "description": "Update technical documents", "resource": "technical_documents", "action": "update"},
        {"name": "technical_documents.delete", "description": "Delete technical documents", "resource": "technical_documents", "action": "delete"},
    ]
    
    created_permissions = []
    for perm_data in permissions:
        permission = Permission(
            workspace_id=workspace_id,
            name=perm_data["name"],
            description=perm_data["description"],
            resource=perm_data["resource"],
            action=perm_data["action"]
        )
        session.add(permission)
        session.flush()
        created_permissions.append(permission)
    
    # Assign all permissions to super admin role
    for permission in created_permissions:
        session.execute(
            role_permissions.insert().values(
                role_id=super_admin_role.id,
                permission_id=permission.id
            )
        )
    
    return super_admin_role

def create_workspace_admin_role(session: Session, workspace_id: int):
    """Create workspace admin role with workspace-specific permissions"""
    workspace_admin_role = Role(
        workspace_id=workspace_id,
        name="Workspace Admin",
        description="Administrator for specific workspace",
        is_active=True
    )
    session.add(workspace_admin_role)
    session.flush()
    
    # Create workspace-specific permissions
    permissions = [
        {"name": "workspace.projects.manage", "description": "Manage projects in workspace", "resource": "projects", "action": "manage"},
        {"name": "workspace.lots.manage", "description": "Manage lots in workspace", "resource": "lots", "action": "manage"},
        {"name": "workspace.documents.manage", "description": "Manage documents in workspace", "resource": "documents", "action": "manage"},
        {"name": "workspace.members.manage", "description": "Manage workspace members", "resource": "workspace_members", "action": "manage"},
    ]
    
    created_permissions = []
    for perm_data in permissions:
        permission = Permission(
            workspace_id=workspace_id,
            name=perm_data["name"],
            description=perm_data["description"],
            resource=perm_data["resource"],
            action=perm_data["action"]
        )
        session.add(permission)
        session.flush()
        created_permissions.append(permission)
    
    # Assign permissions to workspace admin role
    for permission in created_permissions:
        session.execute(
            role_permissions.insert().values(
                role_id=workspace_admin_role.id,
                permission_id=permission.id
            )
        )
    
    return workspace_admin_role

async def seed_database():
    """Main seeding function"""
    print("🌱 Starting database seeding...")
    
    # Create database session
    engine = create_engine(settings.DATABASE_URL)
    SessionLocal.configure(bind=engine)
    
    with SessionLocal() as session:
        try:
            # Check if data already exists
            existing_users = session.query(User).count()
            if existing_users > 0:
                print("⚠️  Database already contains data. Skipping seeding.")
                return
            
            # Create users
            print("👥 Creating test users...")
            users = []
            for user_data in TEST_USERS:
                user = User(
                    supabase_uid=f"test-{user_data['email']}",
                    email=user_data['email'],
                    first_name=user_data['first_name'],
                    last_name=user_data['last_name'],
                    is_active=True,
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                session.add(user)
                session.flush()
                users.append(user)
                print(f"✅ Created user: {user.email}")
            
            # Create workspace
            print("🏢 Creating test workspace...")
            workspace = Workspace(
                name=TEST_WORKSPACE["name"],
                slug=TEST_WORKSPACE["slug"],
                description=TEST_WORKSPACE["description"],
                owner_id=users[0].id,  # Super admin is owner
                is_active=True,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            session.add(workspace)
            session.flush()
            print(f"✅ Created workspace: {workspace.name}")
            
            # Create workspace settings
            workspace_settings = WorkspaceSettings(
                workspace_id=workspace.id,
                default_currency="EUR",
                timezone="Europe/Paris",
                date_format="DD/MM/YYYY",
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            session.add(workspace_settings)
            
            # Create roles
            print("🔐 Creating roles and permissions...")
            super_admin_role = create_super_admin_role(session, workspace.id)
            workspace_admin_role = create_workspace_admin_role(session, workspace.id)
            
            # Assign users to workspace
            print("👥 Assigning users to workspace...")
            # Super admin as workspace owner
            super_admin_member = WorkspaceMember(
                workspace_id=workspace.id,
                user_id=users[0].id,
                role="owner",
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            session.add(super_admin_member)
            
            # Test user as workspace admin
            test_admin_member = WorkspaceMember(
                workspace_id=workspace.id,
                user_id=users[1].id,
                role="admin",
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            session.add(test_admin_member)
            
            # Create test company
            print("🏗️ Creating test company...")
            company = TCompany(
                workspace_id=workspace.id,
                name=TEST_COMPANY["name"],
                siret=TEST_COMPANY["siret"],
                address=TEST_COMPANY["address"],
                phone=TEST_COMPANY["phone"],
                email=TEST_COMPANY["email"],
                website=TEST_COMPANY["website"],
                is_active=True,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            session.add(company)
            session.flush()
            print(f"✅ Created company: {company.name}")
            
            # Create test project
            print("📋 Creating test project...")
            project = Project(
                workspace_id=workspace.id,
                name=TEST_PROJECT["name"],
                description=TEST_PROJECT["description"],
                status=TEST_PROJECT["status"],
                nature=TEST_PROJECT["nature"],
                start_date=TEST_PROJECT["start_date"],
                end_date=TEST_PROJECT["end_date"],
                address=TEST_PROJECT["address"],
                client_name=TEST_PROJECT["client_name"],
                budget=TEST_PROJECT["budget"],
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            session.add(project)
            session.flush()
            print(f"✅ Created project: {project.name}")
            
            # Create test lot
            print("🏗️ Creating test lot...")
            lot = Lot(
                project_id=project.id,
                name=TEST_LOT["name"],
                description=TEST_LOT["description"],
                phase=TEST_LOT["phase"],
                start_date=TEST_LOT["start_date"],
                end_date=TEST_LOT["end_date"],
                budget=TEST_LOT["budget"],
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            session.add(lot)
            session.flush()
            print(f"✅ Created lot: {lot.name}")
            
            # Commit all changes
            session.commit()
            
            print("\n🎉 Database seeding completed successfully!")
            print("\n📊 Summary:")
            print(f"   • Users: {len(users)} created")
            print(f"   • Workspaces: 1 created")
            print(f"   • Projects: 1 created")
            print(f"   • Lots: 1 created")
            print(f"   • Companies: 1 created")
            print(f"   • Roles: 2 created with full permissions")
            
            print("\n🔑 Test Credentials:")
            print(f"   • Super Admin: <EMAIL> / orbis123!")
            print(f"   • Workspace Admin: <EMAIL> / orbis123!")
            print(f"   • Workspace: {TEST_WORKSPACE['slug']}")
            print(f"   • Project: {TEST_PROJECT['name']}")
            print(f"   • Lot: {TEST_LOT['name']}")
            
        except Exception as e:
            session.rollback()
            print(f"❌ Error during seeding: {str(e)}")
            raise
        finally:
            session.close()

if __name__ == "__main__":
    # Ensure we're in the right directory
    os.chdir(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    # Run migrations first
    print("🔄 Running database migrations...")
    os.system("alembic upgrade head")
    
    # Seed the database
    asyncio.run(seed_database())