# app/services/chatgpt_service.py
"""
Service pour l'interaction avec l'API ChatGPT/OpenAI
"""

import asyncio
import logging
from typing import Dict
from datetime import datetime
import openai
from openai import AsyncOpenAI

from app.core.config import settings

logger = logging.getLogger(__name__)

class ChatGPTService:
    """Service pour l'interaction avec l'API OpenAI"""

    def __init__(self):
        # Charger la clé API depuis les variables d'environnement
        # Variable d'environnement requise: OPENAI_API_KEY
        self.api_key = settings.OPENAI_API_KEY
        self.model = getattr(settings, 'OPENAI_MODEL', 'gpt-4.1')
        self.max_retries = 3
        self.timeout = 30
        self.client = None

        # Initialiser le client seulement si la clé API est disponible
        if self.api_key and self.api_key != "your-openai-api-key-here":
            try:
                self.client = AsyncOpenAI(api_key=self.api_key)
                logger.info("Service ChatGPT initialisé avec succès")
            except Exception as e:
                logger.warning(f"Impossible d'initialiser le service ChatGPT: {e}")
                self.client = None
        else:
            logger.warning("Clé API OpenAI non configurée - Service ChatGPT désactivé")
            logger.info("Pour activer ChatGPT, définissez la variable d'environnement OPENAI_API_KEY")

    def is_configured(self) -> bool:
        """Vérifie si le service ChatGPT est correctement configuré"""
        return self.client is not None and self.api_key is not None

    def get_configuration_status(self) -> Dict[str, any]:
        """Retourne le statut de configuration du service"""
        return {
            "configured": self.is_configured(),
            "api_key_present": bool(self.api_key and self.api_key != "your-openai-api-key-here"),
            "model": self.model,
            "client_initialized": self.client is not None
        }

    def is_available(self) -> bool:
        """Vérifie si le service ChatGPT est disponible"""
        return self.client is not None

    async def send_prompt(self, prompt: str) -> Dict[str, any]:
        """
        Envoie un prompt à ChatGPT et retourne la réponse

        Args:
            prompt: Le prompt à envoyer à ChatGPT

        Returns:
            Dict contenant la réponse et les métadonnées
        """
        start_time = datetime.now()

        try:
            # Vérifier que le service est disponible
            if not self.client:
                raise ValueError("Service ChatGPT non disponible - Clé API non configurée")

            # Validation des paramètres
            if not prompt.strip():
                raise ValueError("Le prompt ne peut pas être vide")

            # Appel à l'API OpenAI avec retry
            response_text = await self._call_openai_with_retry(prompt)

            processing_time = (datetime.now() - start_time).total_seconds()

            logger.info(f"Prompt traité avec succès - Temps: {processing_time:.2f}s")

            return {
                "prompt": prompt,
                "response": response_text,
                "processing_time": processing_time,
                "model_used": self.model,
                "success": True
            }

        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"Erreur lors du traitement du prompt: {str(e)}")

            return {
                "prompt": prompt,
                "response": "",
                "processing_time": processing_time,
                "model_used": self.model,
                "success": False,
                "error": str(e)
            }

    async def _call_openai_with_retry(self, prompt: str) -> str:
        """
        Appelle l'API OpenAI avec mécanisme de retry
        """
        last_exception = None

        for attempt in range(self.max_retries):
            try:
                logger.debug(f"Tentative {attempt + 1}/{self.max_retries} d'appel à OpenAI...")

                response = await asyncio.wait_for(
                    self.client.chat.completions.create(
                        model=self.model,
                        messages=[
                            {
                                "role": "user",
                                "content": prompt
                            }
                        ],
                        max_tokens=2000,
                        temperature=0.7
                    ),
                    timeout=self.timeout
                )

                content = response.choices[0].message.content.strip()
                logger.debug(f"Réponse reçue: {len(content)} caractères")

                return content

            except asyncio.TimeoutError as e:
                last_exception = e
                logger.warning(f"Timeout lors de l'appel OpenAI (tentative {attempt + 1}/{self.max_retries})")
                if attempt < self.max_retries - 1:
                    wait_time = 2 ** attempt
                    await asyncio.sleep(wait_time)

            except openai.RateLimitError as e:
                last_exception = e
                logger.warning(f"Rate limit atteint (tentative {attempt + 1}/{self.max_retries})")
                if attempt < self.max_retries - 1:
                    wait_time = 5 * (attempt + 1)
                    await asyncio.sleep(wait_time)

            except openai.APIError as e:
                last_exception = e
                logger.error(f"Erreur API OpenAI (tentative {attempt + 1}/{self.max_retries}): {str(e)}")
                if attempt < self.max_retries - 1:
                    wait_time = 2 ** attempt
                    await asyncio.sleep(wait_time)

            except Exception as e:
                last_exception = e
                logger.error(f"Erreur inattendue (tentative {attempt + 1}/{self.max_retries}): {str(e)}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(1)

        # Si tous les essais ont échoué
        logger.error("Échec de tous les appels à OpenAI")
        raise last_exception or Exception("Échec de tous les appels à l'API OpenAI")

    async def ajout_article_cctp(self, article_data: Dict[str, any]) -> Dict[str, any]:
        """
        Génère un article de CCTP avec ChatGPT basé sur les données du formulaire

        Args:
            article_data: Dictionnaire contenant les données du formulaire d'ajout d'article

        Returns:
            Dict contenant l'article généré et les métadonnées
        """
        start_time = datetime.now()

        try:
            # Vérifier que le service est disponible
            if not self.client:
                raise ValueError("Service ChatGPT non disponible - Clé API non configurée")

            # Validation des paramètres requis
            required_fields = ['prestation', 'localisation', 'unite', 'quantite']
            for field in required_fields:
                if not article_data.get(field):
                    raise ValueError(f"Le champ '{field}' est requis")

            # Construire le prompt avec les variables du formulaire
            prompt = self._build_article_prompt(article_data)
            logger.info(f"PROMPT: {prompt}")

            # Appel à la méthode générique send_prompt
            result = await self.send_prompt(prompt)

            if not result.get("success", False):
                raise ValueError(f"Erreur lors de l'appel ChatGPT: {result.get('error', 'Erreur inconnue')}")

            processing_time = (datetime.now() - start_time).total_seconds()

            logger.info(f"Article CCTP généré avec succès - Temps: {processing_time:.2f}s")

            return {
                "article_content": result["response"],
                "article_data": article_data,
                "processing_time": processing_time,
                "model_used": self.model,
                "success": True
            }

        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"Erreur lors de la génération de l'article CCTP: {str(e)}")

            return {
                "article_content": "",
                "article_data": article_data,
                "processing_time": processing_time,
                "model_used": self.model,
                "success": False,
                "error": str(e)
            }

    def _build_article_prompt(self, article_data: Dict[str, any]) -> str:
        """
        Construit le prompt pour la génération d'article CCTP

        Args:
            article_data: Données du formulaire

        Returns:
            Le prompt formaté pour ChatGPT
        """
        # Extraction de toutes les variables disponibles
        prestation = article_data.get('prestation', '')
        localisation = article_data.get('localisation', '')
        unite = article_data.get('unite', '')
        quantite = article_data.get('quantite', '')

        # Informations produit (optionnelles)
        marque = article_data.get('marque', '')
        reference = article_data.get('reference', '')
        nature = article_data.get('nature', '')
        criteres_qualite = article_data.get('criteresQualite', '')
        dimensions = article_data.get('dimensions', '')
        couleur = article_data.get('couleur', '')
        particularite = article_data.get('particularite', '')

        # Informations pose (optionnelles)
        description_pose = article_data.get('descriptionPose', '')
        type_pose = article_data.get('typePose', '')
        marque_pose = article_data.get('marquePose', '')
        reference_pose = article_data.get('referencePose', '')

        # Options d'inclusion (booléens)
        inclure_criteres = article_data.get('inclureCriteres', False)
        inclure_docs = article_data.get('inclureDocs', False)

        # TODO: Remplacer par le vrai prompt fourni par l'utilisateur
        # Toutes les variables sont prêtes à être utilisées dans le prompt
        prompt = f"""
Rédiger un article de CCTP (Cahier des Clauses Techniques Particulières) conforme
aux usages professionnels du secteur bâtiment et travaux publics. La rédaction doit être
structurée selon le schéma détaillé ci-dessous, sans numérotation apparente. Le contenu doit
être clair, précis, et intégrer un maximum de données techniques, réglementaires et
qualitatives, en veillant à la cohérence du vocabulaire et à la rigueur normative.

Prestation à réaliser :
• Intitulé de la prestation : {prestation}
• Localisation de l’ouvrage : {localisation}

Fournitures, matériaux ou systèmes à intégrer :"""

        if marque or reference:
            prompt += f"""• Marque et référence technique, avec mention « ou techniquement équivalent » si renseignée :
Marque : {marque or ''} référence : {reference or ''}"""

        if nature or criteres_qualite or dimensions or couleur:
            prompt += "Informations complémentaires à intégrer le cas échéant :\n"
        if nature:
            prompt += f"o Nature des matériaux : {nature}\n"
        if criteres_qualite and inclure_criteres:
            prompt += f"o Critères spécifiques de qualité : {criteres_qualite}\n"
        if dimensions:
            prompt += f"o Dimensions précises : {dimensions}\n"
        if couleur:
            prompt += f"o Couleur : {couleur}\n"

        prompt += """\n• Caractéristiques et spécifications détaillées : (à renseigner précisément)
• Rechercher sur le web, en priorité sur le site officiel du fabricant, une description technique
complète incluant toutes les données techniques, acoustique, et incendie.

Mise en œuvre de la prestation :
"""
        if particularite:
            prompt += f"• Particularités spécifiques si renseignées : {particularite}\n"
        if description_pose:
            prompt += f"• Description particulière de la mise en œuvre si renseignée : {description_pose}\n"
        if type_pose:
            prompt += f"• Type de pose (dans le cas où une précision est fournie) : {type_pose}\n"
        if marque_pose or reference_pose:
            prompt += f"""• Matériel de pose associé :
Marque : {marque_pose or ''} référence : {reference_pose or ''}\n"""

        prompt += """• Modalités très détaillées de pose, réalisation ou installation : méthodes, outils, séquençage,
coordination avec les autres corps d’état.
• Conformité aux réglementations en vigueur : mentionner DTU concernés, normes NF ou
EN, Eurocodes applicables, réglementation acoustique ou incendie selon cas
• Recommandations issues des guides professionnels, DTA, avis techniques, CPT, documents
d’application du fabricant ou retours d’expérience BTP

Critères de contrôle, essais, mise en service et réception :
• Préciser quels documents seront exigés à réception : procès-verbaux de vérification de débit,
rapport d’équilibrage, attestations de conformité, certificats de performance, rapport
acoustique, PV essais d’étanchéité, notice d’équipement, dossier OPR

Documents techniques associés aux matériaux utilisés :
"""
        if inclure_docs:
         prompt += "• Lister les fiches techniques produit (datasheet), FDES, DOP, PV d’essais en laboratoires, notices fabricant, DTA ou avis technique, certificat CE, etc.\n"

        prompt += f"""
À préciser à la fin :
• Unité de mesure : {unite}
• Quantité : {quantite}
"""

        return prompt.strip()


# Instance globale du service
chatgpt_service = ChatGPTService()
