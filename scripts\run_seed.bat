@echo off
echo 🌱 Seeding ORBIS Backend V2 with test data...
echo.

REM Run database migrations
echo 🔄 Running migrations...
alembic upgrade head
if %errorlevel% neq 0 (
    echo ❌ Migration failed
    pause
    exit /b 1
)

REM Run seed script
echo 🌱 Seeding test data...
python scripts/seed_test_data.py
if %errorlevel% neq 0 (
    echo ❌ Seeding failed
    pause
    exit /b 1
)

echo.
echo ✅ Test data seeding completed successfully!
echo.
echo 🔑 Test Credentials:
echo    • Super Admin: <EMAIL> / orbis123!
echo    • Workspace Admin: <EMAIL> / orbis123!
echo    • Workspace: test-workspace
echo    • Project: Test Construction Project
echo    • Lot: Test Lot 1 - Structural Work
echo.
pause