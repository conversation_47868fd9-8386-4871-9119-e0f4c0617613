# app/models/workspace.py
from sqlalchemy import Column, Integer, String, DateTime, Text, Foreign<PERSON>ey, <PERSON>olean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.session import Base

class Workspace(Base):
    __tablename__ = "workspaces"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text)
    slug = Column(String(100), unique=True, index=True)
    logo_url = Column(String(500))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    settings = relationship("WorkspaceSettings", back_populates="workspace", uselist=False)
    projects = relationship("Project", back_populates="workspace")
    user_roles = relationship("WorkspaceUserRole", back_populates="workspace")

class WorkspaceSettings(Base):
    __tablename__ = "workspace_settings"
    
    id = Column(Integer, primary_key=True, index=True)
    workspace_id = Column(Integer, ForeignKey("workspaces.id", ondelete="CASCADE"), nullable=False, unique=True)
    default_currency = Column(String(3), default="EUR")
    timezone = Column(String(50), default="UTC")
    date_format = Column(String(20), default="DD/MM/YYYY")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    workspace = relationship("Workspace", back_populates="settings")
