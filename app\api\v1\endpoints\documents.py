# app/api/v1/endpoints/documents.py
from fastapi import APIRouter, Depends, HTTPException, status, Query, UploadFile, File
from sqlalchemy.orm import Session
from typing import List, Optional
from app.db.session import get_db
from app.models.document import Document
from app.schemas.document import DocumentCreate, DocumentUpdate, DocumentResponse
from app.middleware.auth_middleware import get_current_user

router = APIRouter()

@router.post("/", response_model=DocumentResponse)
async def create_document(
    document: DocumentCreate,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Créer un nouveau document"""
    from app.models.rbac import WorkspaceUserRole
    
    # Vérifier l'accès au workspace
    access = db.query(WorkspaceUserRole).filter(
        WorkspaceUserRole.workspace_id == document.workspace_id,
        WorkspaceUserRole.user_id == current_user["id"]
    ).first()
    
    if not access:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this workspace"
        )
    
    db_document = Document(**document.dict())
    db.add(db_document)
    db.commit()
    db.refresh(db_document)
    
    return db_document

@router.get("/", response_model=List[DocumentResponse])
async def get_documents(
    workspace_id: int = Query(..., description="ID du workspace"),
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Récupérer tous les documents d'un workspace"""
    from app.models.rbac import WorkspaceUserRole
    
    # Vérifier l'accès au workspace
    access = db.query(WorkspaceUserRole).filter(
        WorkspaceUserRole.workspace_id == workspace_id,
        WorkspaceUserRole.user_id == current_user["id"]
    ).first()
    
    if not access:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this workspace"
        )
    
    documents = db.query(Document).filter(
        Document.workspace_id == workspace_id,
        Document.is_active == True
    ).all()
    
    return documents

@router.get("/{document_id}", response_model=DocumentResponse)
async def get_document(
    document_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Récupérer un document spécifique"""
    from app.models.rbac import WorkspaceUserRole
    
    document = db.query(Document).join(
        WorkspaceUserRole,
        Document.workspace_id == WorkspaceUserRole.workspace_id
    ).filter(
        Document.id == document_id,
        WorkspaceUserRole.user_id == current_user["id"],
        Document.is_active == True
    ).first()
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    
    return document

@router.put("/{document_id}", response_model=DocumentResponse)
async def update_document(
    document_id: int,
    document: DocumentUpdate,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Mettre à jour un document"""
    from app.models.rbac import WorkspaceUserRole
    
    db_document = db.query(Document).join(
        WorkspaceUserRole,
        Document.workspace_id == WorkspaceUserRole.workspace_id
    ).filter(
        Document.id == document_id,
        WorkspaceUserRole.user_id == current_user["id"]
    ).first()
    
    if not db_document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    
    for field, value in document.dict(exclude_unset=True).items():
        setattr(db_document, field, value)
    
    db.commit()
    db.refresh(db_document)
    
    return db_document

@router.delete("/{document_id}")
async def delete_document(
    document_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Supprimer un document (soft delete)"""
    from app.models.rbac import WorkspaceUserRole
    
    db_document = db.query(Document).join(
        WorkspaceUserRole,
        Document.workspace_id == WorkspaceUserRole.workspace_id
    ).filter(
        Document.id == document_id,
        WorkspaceUserRole.user_id == current_user["id"]
    ).first()
    
    if not db_document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    
    db_document.is_active = False
    db.commit()
    
    return {"message": "Document deleted successfully"}

@router.post("/{document_id}/upload")
async def upload_document(
    document_id: int,
    file: UploadFile = File(...),
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Uploader un fichier pour un document"""
    from app.models.rbac import WorkspaceUserRole
    
    db_document = db.query(Document).join(
        WorkspaceUserRole,
        Document.workspace_id == WorkspaceUserRole.workspace_id
    ).filter(
        Document.id == document_id,
        WorkspaceUserRole.user_id == current_user["id"]
    ).first()
    
    if not db_document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    
    # Ici vous implémenteriez le stockage du fichier
    # Pour l'instant, on retourne une URL simulée
    file_path = f"/uploads/documents/{document_id}/{file.filename}"
    db_document.file_path = file_path
    db_document.file_size = 0  # Calculer la taille réelle
    db_document.mime_type = file.content_type
    db.commit()
    
    return {
        "message": "File uploaded successfully",
        "file_path": file_path
    }

@router.get("/{document_id}/download")
async def download_document(
    document_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Télécharger un fichier de document"""
    from app.models.rbac import WorkspaceUserRole
    
    document = db.query(Document).join(
        WorkspaceUserRole,
        Document.workspace_id == WorkspaceUserRole.workspace_id
    ).filter(
        Document.id == document_id,
        WorkspaceUserRole.user_id == current_user["id"],
        Document.is_active == True
    ).first()
    
    if not document or not document.file_path:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document or file not found"
        )
    
    return {
        "file_path": document.file_path,
        "file_name": document.name,
        "mime_type": document.mime_type
    }
