#!/usr/bin/env python3
"""
Script pour initialiser les actions génératives par défaut
"""

import asyncio
import os
import sys
from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.editor_generative import (
    EditorGenerativeAction,
    EditorGenerativePrompt,
    EditorGenerativeVariable,
    EditorGenerativePromptVariable
)
from app.models.workspace import Workspace

# Ajouter le chemin racine au PYTHONPATH
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def create_default_actions(db: Session, workspace_id: int):
    """Créer les actions génératives par défaut pour un workspace"""
    
    # Actions par défaut
    default_actions = [
        {
            "action_key": "addArticleCCTP",
            "label": "Ajouter un article de CCTP",
            "description": "Génère un article complet pour le Cahier des Clauses Techniques Particulières",
            "category": "cctp"
        },
        {
            "action_key": "enhanceText",
            "label": "Améliorer le texte",
            "description": "Améliore et professionnalise le texte fourni",
            "category": "text"
        },
        {
            "action_key": "generateLOT",
            "label": "Générer un LOT",
            "description": "Génère une description détaillée pour un lot de travaux",
            "category": "lot"
        },
        {
            "action_key": "translateDocument",
            "label": "Traduire un document",
            "description": "Traduit le document dans la langue spécifiée",
            "category": "translation"
        }
    ]
    
    # Variables par défaut pour addArticleCCTP
    cctp_variables = [
        {
            "variable_key": "prestation",
            "variable_name": "Prestation à réaliser",
            "variable_type": "string",
            "description": "Description de la prestation à réaliser",
            "example_value": "Pose de carrelage en intérieur",
            "validation_rules": {"required": True, "min_length": 3}
        },
        {
            "variable_key": "localisation",
            "variable_name": "Localisation",
            "variable_type": "string",
            "description": "Emplacement de la prestation",
            "example_value": "Salle de bain principale",
            "validation_rules": {"required": True}
        },
        {
            "variable_key": "unite",
            "variable_name": "Unité de mesure",
            "variable_type": "string",
            "description": "Unité de mesure pour la quantité",
            "example_value": "m²",
            "validation_rules": {"required": True}
        },
        {
            "variable_key": "quantite",
            "variable_name": "Quantité",
            "variable_type": "number",
            "description": "Quantité à réaliser",
            "example_value": "25.5",
            "validation_rules": {"required": True, "min": 0}
        },
        {
            "variable_key": "marque",
            "variable_name": "Marque",
            "variable_type": "string",
            "description": "Marque du matériel",
            "example_value": "Saint-Gobain",
            "validation_rules": {"required": False}
        },
        {
            "variable_key": "reference",
            "variable_name": "Référence",
            "variable_type": "string",
            "description": "Référence du produit",
            "example_value": "Carrelage 30x30 cm",
            "validation_rules": {"required": False}
        },
        {
            "variable_key": "nature",
            "variable_name": "Nature du matériel",
            "variable_type": "string",
            "description": "Type de matériel",
            "example_value": "Carrelage grès émaillé",
            "validation_rules": {"required": True}
        },
        {
            "variable_key": "dimensions",
            "variable_name": "Dimensions",
            "variable_type": "string",
            "description": "Dimensions du matériel",
            "example_value": "30x30 cm",
            "validation_rules": {"required": False}
        },
        {
            "variable_key": "couleur",
            "variable_name": "Couleur",
            "variable_type": "string",
            "description": "Couleur du matériel",
            "example_value": "Blanc",
            "validation_rules": {"required": False}
        },
        {
            "variable_key": "particularite",
            "variable_name": "Particularité",
            "variable_type": "string",
            "description": "Caractéristiques particulières",
            "example_value": "Antidérapant R11",
            "validation_rules": {"required": False}
        }
    ]
    
    # Prompt par défaut pour addArticleCCTP
    cctp_prompt = """Rédiger un article de CCTP (Cahier des Clauses Techniques Particulières) conforme aux usages professionnels du secteur bâtiment et travaux publics.

Prestation à réaliser : {prestation}
Localisation : {localisation}
Unité : {unite}
Quantité : {quantite}

Marque : {marque}
Référence : {reference}
Nature : {nature}
Dimensions : {dimensions}
Couleur : {couleur}
Particularité : {particularite}

L'article doit inclure :
1. Description détaillée de la prestation
2. Spécifications techniques
3. Conditions de mise en œuvre
4. Critères de réception
5. Références normatives si applicable"""

    # Créer les actions
    for action_data in default_actions:
        # Vérifier si l'action existe déjà
        existing_action = db.query(EditorGenerativeAction).filter(
            EditorGenerativeAction.workspace_id == workspace_id,
            EditorGenerativeAction.action_key == action_data["action_key"]
        ).first()
        
        if existing_action:
            print(f"Action {action_data['action_key']} existe déjà")
            continue
        
        # Créer l'action
        action = EditorGenerativeAction(
            workspace_id=workspace_id,
            **action_data
        )
        db.add(action)
        db.commit()
        db.refresh(action)
        
        print(f"Action créée : {action_data['action_key']}")
        
        # Pour addArticleCCTP, créer le prompt et les variables
        if action_data["action_key"] == "addArticleCCTP":
            # Créer le prompt
            prompt = EditorGenerativePrompt(
                workspace_id=workspace_id,
                action_id=action.id,
                prompt_template=cctp_prompt,
                is_default=True
            )
            db.add(prompt)
            db.commit()
            db.refresh(prompt)
            
            # Créer les variables
            for var_data in cctp_variables:
                # Vérifier si la variable existe déjà
                existing_var = db.query(EditorGenerativeVariable).filter(
                    EditorGenerativeVariable.workspace_id == workspace_id,
                    EditorGenerativeVariable.variable_key == var_data["variable_key"]
                ).first()
                
                if existing_var:
                    variable = existing_var
                else:
                    variable = EditorGenerativeVariable(
                        workspace_id=workspace_id,
                        **var_data
                    )
                    db.add(variable)
                    db.commit()
                    db.refresh(variable)
                
                # Associer la variable au prompt
                prompt_variable = EditorGenerativePromptVariable(
                    prompt_id=prompt.id,
                    variable_id=variable.id,
                    is_required=var_data["validation_rules"].get("required", False),
                    order_index=cctp_variables.index(var_data)
                )
                db.add(prompt_variable)
            
            db.commit()
            print(f"Variables et prompt créés pour {action_data['action_key']}")

def main():
    """Script principal"""
    db = SessionLocal()
    
    try:
        # Récupérer tous les workspaces
        workspaces = db.query(Workspace).all()
        
        if not workspaces:
            print("Aucun workspace trouvé")
            return
        
        for workspace in workspaces:
            print(f"\n🎯 Initialisation des actions pour le workspace: {workspace.name} (ID: {workspace.id})")
            create_default_actions(db, workspace.id)
        
        print("\n✅ Initialisation terminée avec succès")
        
    except Exception as e:
        print(f"❌ Erreur lors de l'initialisation: {str(e)}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    main()
