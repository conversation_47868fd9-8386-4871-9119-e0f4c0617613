# app/schemas/technical_document.py
from pydantic import BaseModel, Field
from typing import Optional

class TechnicalDocumentBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=255)
    content: Optional[str] = None
    document_type: Optional[str] = Field(None, max_length=50)
    indice: Optional[str] = Field(None, max_length=10)
    version: Optional[int] = Field(None, ge=1)

class TechnicalDocumentCreate(TechnicalDocumentBase):
    workspace_id: int
    project_id: int
    lot_id: Optional[int] = None

class TechnicalDocumentUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    content: Optional[str] = None
    document_type: Optional[str] = Field(None, max_length=50)
    indice: Optional[str] = Field(None, max_length=10)
    version: Optional[int] = Field(None, ge=1)

class TechnicalDocumentResponse(TechnicalDocumentBase):
    id: int
    workspace_id: int
    project_id: int
    lot_id: Optional[int] = None
    is_active: bool
    created_at: str
    updated_at: Optional[str] = None
    
    class Config:
        from_attributes = True
