# app/api/v1/endpoints/projects.py
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from app.db.session import get_db
from app.models.project import Project
from app.schemas.project import ProjectCreate, ProjectUpdate, ProjectResponse
from app.middleware.auth_middleware import get_current_user

router = APIRouter()

@router.post("/", response_model=ProjectResponse)
async def create_project(
    project: ProjectCreate,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Créer un nouveau projet"""
    from app.models.rbac import WorkspaceUserRole
    
    # Vérifier l'accès au workspace
    access = db.query(WorkspaceUserRole).filter(
        WorkspaceUserRole.workspace_id == project.workspace_id,
        WorkspaceUserRole.user_id == current_user["id"]
    ).first()
    
    if not access:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this workspace"
        )
    
    db_project = Project(**project.dict())
    db.add(db_project)
    db.commit()
    db.refresh(db_project)
    
    return db_project

@router.get("/", response_model=List[ProjectResponse])
async def get_projects(
    workspace_id: int = Query(..., description="ID du workspace"),
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Récupérer tous les projets d'un workspace"""
    from app.models.rbac import WorkspaceUserRole
    
    # Vérifier l'accès au workspace
    access = db.query(WorkspaceUserRole).filter(
        WorkspaceUserRole.workspace_id == workspace_id,
        WorkspaceUserRole.user_id == current_user["id"]
    ).first()
    
    if not access:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this workspace"
        )
    
    projects = db.query(Project).filter(
        Project.workspace_id == workspace_id,
        Project.is_active == True
    ).all()
    
    return projects

@router.get("/{project_id}", response_model=ProjectResponse)
async def get_project(
    project_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Récupérer un projet spécifique"""
    from app.models.rbac import WorkspaceUserRole
    
    project = db.query(Project).join(
        WorkspaceUserRole,
        Project.workspace_id == WorkspaceUserRole.workspace_id
    ).filter(
        Project.id == project_id,
        WorkspaceUserRole.user_id == current_user["id"],
        Project.is_active == True
    ).first()
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    return project

@router.put("/{project_id}", response_model=ProjectResponse)
async def update_project(
    project_id: int,
    project: ProjectUpdate,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Mettre à jour un projet"""
    from app.models.rbac import WorkspaceUserRole
    
    db_project = db.query(Project).join(
        WorkspaceUserRole,
        Project.workspace_id == WorkspaceUserRole.workspace_id
    ).filter(
        Project.id == project_id,
        WorkspaceUserRole.user_id == current_user["id"]
    ).first()
    
    if not db_project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    for field, value in project.dict(exclude_unset=True).items():
        setattr(db_project, field, value)
    
    db.commit()
    db.refresh(db_project)
    
    return db_project

@router.delete("/{project_id}")
async def delete_project(
    project_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Supprimer un projet (soft delete)"""
    from app.models.rbac import WorkspaceUserRole
    
    db_project = db.query(Project).join(
        WorkspaceUserRole,
        Project.workspace_id == WorkspaceUserRole.workspace_id
    ).filter(
        Project.id == project_id,
        WorkspaceUserRole.user_id == current_user["id"]
    ).first()
    
    if not db_project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    db_project.is_active = False
    db.commit()
    
    return {"message": "Project deleted successfully"}
