# PowerShell script to seed test data
Write-Host "🌱 Seeding ORBIS Backend V2 with test data..." -ForegroundColor Green

# Run migrations
Write-Host "🔄 Running migrations..." -ForegroundColor Yellow
alembic upgrade head
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Migration failed" -ForegroundColor Red
    exit 1
}

# Run seed script
Write-Host "🌱 Seeding test data..." -ForegroundColor Yellow
python scripts/seed_test_data_fixed.py
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Seeding failed" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "✅ Test data seeding completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "🔑 Test Credentials:" -ForegroundColor Cyan
Write-Host "   • Super Admin: <EMAIL> / orbis123!" -ForegroundColor Yellow
Write-Host "   • Workspace Admin: <EMAIL> / orbis123!" -ForegroundColor Yellow
Write-Host "   • Workspace: test-workspace" -ForegroundColor Yellow
Write-Host "   • Project: Test Construction Project" -ForegroundColor Yellow
Write-Host "   • Lot: Test Lot 1 - Structural Work" -ForegroundColor Yellow
Write-Host ""