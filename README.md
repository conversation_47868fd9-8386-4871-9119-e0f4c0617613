# ORBIS Backend V2

Backend optimisé pour l'application ORBIS - Gestion de projets de construction et documents techniques.

## 🚀 Architecture

- **FastAPI** - Framework web moderne et rapide
- **SQLAlchemy** - ORM avec support async
- **PostgreSQL** - Base de données relationnelle
- **Supabase Auth** - Authentification moderne et sécurisée
- **Redis** - Cache pour les permissions
- **Alembic** - Gestion des migrations

## 📊 Optimisations par rapport à V1

| Composant | V1 | V2 |
|-----------|-----|-----|
| Modèles | 15+ | 7 |
| Schémas | 15+ | 8 |
| Services | 5 | 1 (Auth) |
| Auth | JWT custom + Supabase | Supabase uniquement |
| Cache | Non | Redis |
| Types | Enum en dur | BDD administrable |

## 🏗️ Structure

```
orbis-backendV2/
├── app/
│   ├── api/v1/endpoints/     # Routes API
│   ├── core/                 # Configuration
│   ├── db/                   # Base de données
│   ├── middleware/           # Middleware
│   ├── models/               # Modèles SQLAlchemy
│   ├── schemas/              # Schémas Pydantic
│   └── services/             # Services
├── alembic/                  # Migrations
├── alembic/versions/         # Scripts de migration
└── uploads/                  # Fichiers uploadés
```

## 🛠️ Installation

### Prérequis
- Python 3.11+
- PostgreSQL 14+
- Redis 6+

### Installation rapide

```bash
# Cloner le projet
git clone <repository-url>
cd orbis-backendV2

# Créer l'environnement virtuel
python -m venv venv
source venv/bin/activate  # Linux/Mac
# ou
.\venv\Scripts\activate   # Windows

# Installer les dépendances
pip install -r requirements.txt

# Configurer l'environnement
cp .env.example .env
# Éditer .env avec vos configurations

# Créer la base de données
createdb orbis_v2

# Lancer les migrations
alembic upgrade head

# Démarrer le serveur
uvicorn app.main:app --reload
```

## 📋 Configuration

### Variables d'environnement

```bash
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/orbis_v2
ASYNC_DATABASE_URL=postgresql+asyncpg://user:password@localhost:5432/orbis_v2

# Supabase
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Redis
REDIS_URL=redis://localhost:6379

# OpenAI
OPENAI_API_KEY=your-openai-key
```

## 🔧 Scripts de migration

```bash
# Créer une nouvelle migration
alembic revision --autogenerate -m "description"

# Appliquer les migrations
alembic upgrade head

# Revenir à une version précédente
alembic downgrade -1
```

## 🧪 Tests

```bash
# Tests unitaires
pytest tests/

# Tests avec couverture
pytest --cov=app tests/
```

## 📚 Documentation API

La documentation interactive est disponible à :
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 🚀 Déploiement

### Docker
```bash
docker build -t orbis-backend-v2 .
docker run -p 8000:8000 orbis-backend-v2
```

### Render
```bash
# Déploiement automatique via render.yaml
```

## 🔐 Sécurité

- Authentification via Supabase
- Permissions RBAC avec cache Redis
- Validation des entrées avec Pydantic
- Protection CSRF
- Rate limiting

## 📞 Support

Pour toute question ou problème, ouvrez une issue sur GitHub ou contactez l'équipe de développement.
