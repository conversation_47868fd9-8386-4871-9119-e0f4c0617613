"""Add editor generative tables

Revision ID: 002_add_editor_generative_tables
Revises: 001_initial_v2_migration
Create Date: 2025-08-06 09:30:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '002_add_editor_generative_tables'
down_revision = '001_initial_v2'
branch_labels = None
depends_on = None


def upgrade():
    # Create editor_generative_actions table
    op.create_table(
        'editor_generative_actions',
        sa.<PERSON>umn('id', sa.Integer(), nullable=False, autoincrement=True),
        sa.Column('workspace_id', sa.Integer(), nullable=False),
        sa.Column('action_key', sa.String(length=100), nullable=False),
        sa.Column('label', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('category', sa.String(length=100), nullable=False, server_default='general'),
        sa.Column('is_active', sa.<PERSON>(), nullable=False, server_default='true'),
        sa.<PERSON>umn('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()')),
        sa.Column('updated_at', sa.DateTime(timezone=True), onupdate=sa.text('now()')),
        sa.ForeignKeyConstraint(['workspace_id'], ['workspaces.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('workspace_id', 'action_key')
    )
    
    op.create_index(
        'ix_editor_generative_actions_workspace_id',
        'editor_generative_actions',
        ['workspace_id']
    )
    
    op.create_index(
        'ix_editor_generative_actions_action_key',
        'editor_generative_actions',
        ['action_key']
    )
    
    # Create editor_generative_prompts table
    op.create_table(
        'editor_generative_prompts',
        sa.Column('id', sa.Integer(), nullable=False, autoincrement=True),
        sa.Column('workspace_id', sa.Integer(), nullable=False),
        sa.Column('action_id', sa.Integer(), nullable=False),
        sa.Column('prompt_template', sa.Text(), nullable=False),
        sa.Column('model', sa.String(length=100), nullable=False, server_default='gpt-4o'),
        sa.Column('max_tokens', sa.Integer(), nullable=False, server_default='2000'),
        sa.Column('temperature', sa.String(length=10), nullable=False, server_default='0.7'),
        sa.Column('is_active', sa.Boolean(), nullable=False, server_default='true'),
        sa.Column('is_default', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()')),
        sa.Column('updated_at', sa.DateTime(timezone=True), onupdate=sa.text('now()')),
        sa.ForeignKeyConstraint(['action_id'], ['editor_generative_actions.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['workspace_id'], ['workspaces.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    
    op.create_index(
        'ix_editor_generative_prompts_workspace_id',
        'editor_generative_prompts',
        ['workspace_id']
    )
    
    op.create_index(
        'ix_editor_generative_prompts_action_id',
        'editor_generative_prompts',
        ['action_id']
    )
    
    # Create editor_generative_variables table
    op.create_table(
        'editor_generative_variables',
        sa.Column('id', sa.Integer(), nullable=False, autoincrement=True),
        sa.Column('workspace_id', sa.Integer(), nullable=False),
        sa.Column('variable_key', sa.String(length=100), nullable=False),
        sa.Column('variable_name', sa.String(length=255), nullable=False),
        sa.Column('variable_type', sa.String(length=50), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('example_value', sa.Text(), nullable=True),
        sa.Column('validation_rules', sa.JSON(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False, server_default='true'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()')),
        sa.Column('updated_at', sa.DateTime(timezone=True), onupdate=sa.text('now()')),
        sa.ForeignKeyConstraint(['workspace_id'], ['workspaces.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('workspace_id', 'variable_key')
    )
    
    op.create_index(
        'ix_editor_generative_variables_workspace_id',
        'editor_generative_variables',
        ['workspace_id']
    )
    
    op.create_index(
        'ix_editor_generative_variables_variable_key',
        'editor_generative_variables',
        ['variable_key']
    )
    
    # Create editor_generative_prompt_variables table
    op.create_table(
        'editor_generative_prompt_variables',
        sa.Column('id', sa.Integer(), nullable=False, autoincrement=True),
        sa.Column('prompt_id', sa.Integer(), nullable=False),
        sa.Column('variable_id', sa.Integer(), nullable=False),
        sa.Column('is_required', sa.Boolean(), nullable=False, server_default='true'),
        sa.Column('default_value', sa.Text(), nullable=True),
        sa.Column('order_index', sa.Integer(), nullable=False, server_default='0'),
        sa.ForeignKeyConstraint(['prompt_id'], ['editor_generative_prompts.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['variable_id'], ['editor_generative_variables.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('prompt_id', 'variable_id')
    )
    
    op.create_index(
        'ix_editor_generative_prompt_variables_prompt_id',
        'editor_generative_prompt_variables',
        ['prompt_id']
    )
    
    op.create_index(
        'ix_editor_generative_prompt_variables_variable_id',
        'editor_generative_prompt_variables',
        ['variable_id']
    )

    # Enable RLS on new tables for Supabase
    tables = [
        'editor_generative_actions', 'editor_generative_prompts',
        'editor_generative_variables', 'editor_generative_prompt_variables'
    ]
    
    for table in tables:
        op.execute(f'ALTER TABLE {table} ENABLE ROW LEVEL SECURITY;')


def downgrade():
    # Drop tables in reverse order
    op.drop_table('editor_generative_prompt_variables')
    op.drop_table('editor_generative_variables')
    op.drop_table('editor_generative_prompts')
    op.drop_table('editor_generative_actions')
