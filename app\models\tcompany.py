# app/models/tcompany.py
from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.session import Base

class TCompany(Base):
    __tablename__ = "tcompanies"
    
    id = Column(Integer, primary_key=True, index=True)
    workspace_id = Column(Integer, ForeignKey("workspaces.id", ondelete="CASCADE"), nullable=False, index=True)
    name = Column(String(255), nullable=False, index=True)
    code = Column(String(50), nullable=False, index=True)
    type = Column(String(50), default="contractor")  # contractor, supplier, architect, etc.
    siret = Column(String(14))
    address = Column(Text)
    city = Column(String(100))
    postal_code = Column(String(20))
    country = Column(String(50), default="France")
    email = Column(String(255))
    phone = Column(String(50))
    website = Column(String(255))
    logo_url = Column(String(500))
    description = Column(Text)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    workspace = relationship("Workspace")
    stakeholders = relationship("Stakeholder", back_populates="tcompany")
