# alembic.ini
[alembic]
script_location = alembic
prepend_sys_path = .
version_path_separator = os

# Configuration de la base de données - sera surchargée par DATABASE_URL dans env.py
sqlalchemy.url = postgresql://user:password@localhost/orbis_v2

[post_write_hooks]

[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = WARN
handlers = console
qualname =

[logger_sqlalchemy]
level = WARN
handlers =
qualname = sqlalchemy.engine

[logger_alembic]
level = INFO
handlers =
qualname = alembic

[handler_console]
class = StreamHandler
args = (sys.stderr,)
level = NOTSET
formatter = generic

[formatter_generic]
format = %(levelname)-5.5s [%(name)s] %(message)s
datefmt = %H:%M:%S
