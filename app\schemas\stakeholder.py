# app/schemas/stakeholder.py
from pydantic import BaseModel, <PERSON>
from typing import Optional

class StakeholderBase(BaseModel):
    lot_id: int
    tcompany_id: int
    role: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None

class StakeholderCreate(StakeholderBase):
    pass

class StakeholderUpdate(BaseModel):
    role: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None

class StakeholderResponse(StakeholderBase):
    id: int
    is_active: bool
    created_at: str
    updated_at: Optional[str] = None
    
    class Config:
        from_attributes = True
