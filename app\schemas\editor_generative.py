# app/schemas/editor_generative.py
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime

# Base schemas
class EditorGenerativeActionBase(BaseModel):
    action_key: str = Field(..., description="Clé unique de l'action (ex: addArticleCCTP)")
    label: str = Field(..., description="Libellé de l'action (ex: Ajouter un article de CCTP)")
    description: Optional[str] = None
    category: str = Field(default="general", description="Catégorie de l'action")
    is_active: bool = True

class EditorGenerativePromptBase(BaseModel):
    prompt_template: str = Field(..., description="Template du prompt avec variables")
    model: str = Field(default="gpt-4o")
    max_tokens: int = Field(default=2000, ge=100, le=4000)
    temperature: str = Field(default="0.7")
    is_active: bool = True
    is_default: bool = False

class EditorGenerativeVariableBase(BaseModel):
    variable_key: str = Field(..., description="Clé de la variable (ex: prestation)")
    variable_name: str = Field(..., description="Nom lisible de la variable")
    variable_type: str = Field(..., description="Type: string, number, boolean, object, array")
    description: Optional[str] = None
    example_value: Optional[str] = None
    validation_rules: Optional[Dict[str, Any]] = None
    is_active: bool = True

class EditorGenerativePromptVariableBase(BaseModel):
    is_required: bool = True
    default_value: Optional[str] = None
    order_index: int = 0

# Create schemas
class EditorGenerativeActionCreate(EditorGenerativeActionBase):
    workspace_id: int

class EditorGenerativePromptCreate(EditorGenerativePromptBase):
    workspace_id: int
    action_id: int

class EditorGenerativeVariableCreate(EditorGenerativeVariableBase):
    workspace_id: int

class EditorGenerativePromptVariableCreate(EditorGenerativePromptVariableBase):
    prompt_id: int
    variable_id: int

# Update schemas
class EditorGenerativeActionUpdate(BaseModel):
    label: Optional[str] = None
    description: Optional[str] = None
    category: Optional[str] = None
    is_active: Optional[bool] = None

class EditorGenerativePromptUpdate(BaseModel):
    prompt_template: Optional[str] = None
    model: Optional[str] = None
    max_tokens: Optional[int] = None
    temperature: Optional[str] = None
    is_active: Optional[bool] = None
    is_default: Optional[bool] = None

class EditorGenerativeVariableUpdate(BaseModel):
    variable_name: Optional[str] = None
    description: Optional[str] = None
    example_value: Optional[str] = None
    validation_rules: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None

# Response schemas
class EditorGenerativeActionResponse(EditorGenerativeActionBase):
    id: int
    workspace_id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class EditorGenerativePromptResponse(EditorGenerativePromptBase):
    id: int
    workspace_id: int
    action_id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class EditorGenerativeVariableResponse(EditorGenerativeVariableBase):
    id: int
    workspace_id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class EditorGenerativePromptVariableResponse(EditorGenerativePromptVariableBase):
    id: int
    prompt_id: int
    variable_id: int
    
    class Config:
        from_attributes = True

# Detailed response schemas with relationships
class EditorGenerativeActionDetail(EditorGenerativeActionResponse):
    prompts: List[EditorGenerativePromptResponse] = []

class EditorGenerativePromptDetail(EditorGenerativePromptResponse):
    action: EditorGenerativeActionResponse
    variables: List[EditorGenerativeVariableResponse] = []

class EditorGenerativeVariableDetail(EditorGenerativeVariableResponse):
    prompts: List[EditorGenerativePromptResponse] = []

# Execution schemas
class ExecuteGenerativeActionRequest(BaseModel):
    action_key: str
    variables: Dict[str, Any] = Field(default_factory=dict)
    context_data: Optional[Dict[str, Any]] = None

class ExecuteGenerativeActionResponse(BaseModel):
    success: bool
    result: Optional[str] = None
    error: Optional[str] = None
    processing_time: Optional[float] = None
    model_used: Optional[str] = None
    variables_used: Dict[str, Any] = Field(default_factory=dict)
