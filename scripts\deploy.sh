#!/bin/bash
# Deployment script for Render.com

set -e

echo "🚀 Starting ORBIS Backend V2 deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required environment variables are set
check_env_vars() {
    print_status "Checking environment variables..."
    
    required_vars=("SUPABASE_URL" "SUPABASE_ANON_KEY" "SUPABASE_SERVICE_ROLE_KEY" "OPENAI_API_KEY")
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            print_error "Environment variable $var is not set"
            exit 1
        fi
    done
    
    print_status "All required environment variables are set"
}

# Run database migrations
run_migrations() {
    print_status "Running database migrations..."
    
    if command -v alembic &> /dev/null; then
        alembic upgrade head
        print_status "Database migrations completed successfully"
    else
        print_warning "Alembic not found, skipping migrations"
    fi
}

# Create uploads directory
setup_uploads() {
    print_status "Setting up uploads directory..."
    mkdir -p uploads
    chmod 755 uploads
    print_status "Uploads directory created"
}

# Health check
health_check() {
    print_status "Performing health check..."
    
    # Wait for the service to start
    sleep 5
    
    # Check if the service is responding
    if curl -f http://localhost:${PORT:-8000}/health > /dev/null 2>&1; then
        print_status "Health check passed"
    else
        print_error "Health check failed"
        exit 1
    fi
}

# Main deployment flow
main() {
    print_status "Starting deployment process..."
    
    check_env_vars
    setup_uploads
    run_migrations
    
    print_status "✅ Deployment preparation complete"
    print_status "Starting application..."
    
    # Start the application (this will be handled by Render)
    exec uvicorn app.main:app --host 0.0.0.0 --port ${PORT:-8000}
}

# Run main function
main