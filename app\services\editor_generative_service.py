# app/services/editor_generative_service.py
import re
import logging
from typing import Dict, Any, Optional
from datetime import datetime
from sqlalchemy.orm import Session
from app.models.editor_generative import (
    EditorGenerativeAction, 
    EditorGenerativePrompt, 
    EditorGenerativeVariable
)
from app.services.chatgpt_service import chatgpt_service

logger = logging.getLogger(__name__)

class EditorGenerativeService:
    """Service pour gérer l'exécution des actions génératives avec prompts personnalisés"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def execute_action(
        self,
        workspace_id: int,
        action_key: str,
        variables: Dict[str, Any] = None,
        context_data: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Exécuter une action générative avec le prompt personnalisé
        
        Args:
            workspace_id: ID du workspace
            action_key: Clé de l'action (ex: addArticleCCTP)
            variables: Variables à injecter dans le prompt
            context_data: Données de contexte supplémentaires
            
        Returns:
            Dict avec le résultat de l'exécution
        """
        start_time = datetime.now()
        variables = variables or {}
        context_data = context_data or {}
        
        try:
            # Récupérer l'action
            action = self.db.query(EditorGenerativeAction).filter(
                EditorGenerativeAction.workspace_id == workspace_id,
                EditorGenerativeAction.action_key == action_key,
                EditorGenerativeAction.is_active == True
            ).first()
            
            if not action:
                raise ValueError(f"Action '{action_key}' non trouvée ou inactive")
            
            # Récupérer le prompt actif
            prompt = self.db.query(EditorGenerativePrompt).filter(
                EditorGenerativePrompt.action_id == action.id,
                EditorGenerativePrompt.workspace_id == workspace_id,
                EditorGenerativePrompt.is_active == True
            ).order_by(
                EditorGenerativePrompt.is_default.desc(),
                EditorGenerativePrompt.created_at.desc()
            ).first()
            
            if not prompt:
                # Fallback sur le prompt par défaut système
                return await self._execute_default_action(action_key, variables, context_data)
            
            # Récupérer les variables du prompt
            prompt_variables = self._get_prompt_variables(prompt.id)
            
            # Fusionner les variables avec les données de contexte
            all_variables = {**context_data, **variables}
            
            # Valider les variables requises
            validation_result = self._validate_variables(prompt_variables, all_variables)
            if not validation_result["valid"]:
                raise ValueError(f"Variables manquantes: {validation_result['missing']}")
            
            # Remplacer les variables dans le prompt
            processed_prompt = self._process_prompt_template(
                prompt.prompt_template, 
                all_variables, 
                prompt_variables
            )
            
            # Exécuter avec ChatGPT
            if not chatgpt_service.is_configured():
                raise ValueError("Service ChatGPT non configuré")
            
            result = await chatgpt_service.send_prompt(processed_prompt)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return {
                "success": result.get("success", False),
                "result": result.get("response", ""),
                "error": result.get("error", ""),
                "processing_time": processing_time,
                "model_used": prompt.model,
                "variables_used": all_variables
            }
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"Erreur lors de l'exécution de l'action {action_key}: {str(e)}")
            
            return {
                "success": False,
                "result": None,
                "error": str(e),
                "processing_time": processing_time,
                "model_used": None,
                "variables_used": variables
            }
    
    def _get_prompt_variables(self, prompt_id: int) -> Dict[str, Any]:
        """Récupérer les variables associées à un prompt"""
        variables = {}
        
        # Jointure pour récupérer les variables
        query = self.db.query(
            EditorGenerativeVariable.variable_key,
            EditorGenerativeVariable.variable_name,
            EditorGenerativeVariable.variable_type,
            EditorGenerativeVariable.validation_rules,
            EditorGenerativeVariable.example_value
        ).join(
            EditorGenerativePromptVariable,
            EditorGenerativePromptVariable.variable_id == EditorGenerativeVariable.id
        ).filter(
            EditorGenerativePromptVariable.prompt_id == prompt_id,
            EditorGenerativeVariable.is_active == True
        ).order_by(
            EditorGenerativePromptVariable.order_index
        )
        
        for var in query.all():
            variables[var.variable_key] = {
                "name": var.variable_name,
                "type": var.variable_type,
                "validation": var.validation_rules or {},
                "example": var.example_value
            }
        
        return variables
    
    def _validate_variables(self, prompt_variables: Dict[str, Any], provided_variables: Dict[str, Any]) -> Dict[str, Any]:
        """Valider que toutes les variables requises sont fournies"""
        missing = []
        valid = True
        
        for var_key, var_config in prompt_variables.items():
            validation_rules = var_config.get("validation", {})
            
            if validation_rules.get("required", False):
                if var_key not in provided_variables or provided_variables[var_key] is None:
                    missing.append(var_key)
                    valid = False
        
        return {
            "valid": valid,
            "missing": missing
        }
    
    def _process_prompt_template(
        self, 
        template: str, 
        variables: Dict[str, Any], 
        variable_configs: Dict[str, Any]
    ) -> str:
        """Remplacer les variables dans le template du prompt"""
        processed = template
        
        # Remplacer les variables avec la syntaxe {variable_name}
        for var_key, value in variables.items():
            if var_key in variable_configs:
                # Convertir la valeur en string selon le type
                str_value = self._format_variable_value(value, variable_configs[var_key]["type"])
                processed = processed.replace(f"{{{var_key}}}", str_value)
        
        # Remplacer les variables manquantes par des valeurs par défaut ou vides
        for var_key, var_config in variable_configs.items():
            if f"{{{var_key}}}" in processed:
                default_value = var_config.get("validation", {}).get("default", "")
                processed = processed.replace(f"{{{var_key}}}", str(default_value))
        
        return processed
    
    def _format_variable_value(self, value: Any, var_type: str) -> str:
        """Formater une variable selon son type"""
        if value is None:
            return ""
        
        if var_type == "string":
            return str(value)
        elif var_type == "number":
            return str(float(value))
        elif var_type == "boolean":
            return "true" if value else "false"
        elif var_type == "array":
            return ", ".join(str(item) for item in value) if isinstance(value, list) else str(value)
        elif var_type == "object":
            return str(value)
        else:
            return str(value)
    
    async def _execute_default_action(
        self, 
        action_key: str, 
        variables: Dict[str, Any], 
        context_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Exécuter l'action avec le prompt système par défaut"""
        
        default_prompts = {
            "addArticleCCTP": self._get_default_cctp_prompt(),
            "enhanceText": self._get_default_enhance_prompt(),
            "generateLOT": self._get_default_lot_prompt()
        }
        
        if action_key not in default_prompts:
            raise ValueError(f"Action par défaut '{action_key}' non trouvée")
        
        prompt = default_prompts[action_key]
        processed_prompt = self._process_prompt_template(prompt, variables, {})
        
        result = await chatgpt_service.send_prompt(processed_prompt)
        
        return {
            "success": result.get("success", False),
            "result": result.get("response", ""),
            "error": result.get("error", ""),
            "processing_time": None,
            "model_used": "gpt-4o",
            "variables_used": variables
        }
    
    def _get_default_cctp_prompt(self) -> str:
        """Prompt par défaut pour ajouter un article CCTP"""
        return """
Rédiger un article de CCTP (Cahier des Clauses Techniques Particulières) conforme
aux usages professionnels du secteur bâtiment et travaux publics.

Prestation à réaliser : {prestation}
Localisation : {localisation}
Unité : {unite}
Quantité : {quantite}

Marque : {marque}
Référence : {reference}
Nature : {nature}
Dimensions : {dimensions}
Couleur : {couleur}
Particularité : {particularite}
"""
    
    def _get_default_enhance_prompt(self) -> str:
        """Prompt par défaut pour améliorer le texte"""
        return """
Améliore le texte suivant en le rendant plus professionnel et clair :
{original_text}
"""
    
    def _get_default_lot_prompt(self) -> str:
        """Prompt par défaut pour générer un LOT"""
        return """
Génère une description détaillée pour le lot suivant :
Nom : {lot_name}
Type : {lot_type}
Description : {description}
"""
