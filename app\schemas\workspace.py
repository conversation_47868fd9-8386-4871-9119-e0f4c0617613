# app/schemas/workspace.py
from pydantic import BaseModel, <PERSON>
from typing import Optional, List

class WorkspaceBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    slug: Optional[str] = Field(None, max_length=100)
    logo_url: Optional[str] = None

class WorkspaceCreate(WorkspaceBase):
    pass

class WorkspaceUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    logo_url: Optional[str] = None

class WorkspaceResponse(WorkspaceBase):
    id: int
    is_active: bool
    
    class Config:
        from_attributes = True

class WorkspaceSettingsBase(BaseModel):
    default_currency: Optional[str] = Field(None, max_length=3)
    timezone: Optional[str] = Field(None, max_length=50)
    date_format: Optional[str] = Field(None, max_length=20)

class WorkspaceSettingsUpdate(WorkspaceSettingsBase):
    pass

class WorkspaceSettingsResponse(WorkspaceSettingsBase):
    workspace_id: int
    
    class Config:
        from_attributes = True
