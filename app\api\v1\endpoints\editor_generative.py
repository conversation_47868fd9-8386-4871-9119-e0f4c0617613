# app/api/v1/endpoints/editor_generative.py
from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from app.db.session import get_db
from app.models.editor_generative import (
    EditorGenerativeAction, 
    EditorGenerativePrompt, 
    EditorGenerativeVariable,
    EditorGenerativePromptVariable
)
from app.models.workspace import Workspace
from app.models.user import User
from app.schemas.editor_generative import *
from app.services.editor_generative_service import EditorGenerativeService
from app.middleware.auth_middleware import get_current_user

router = APIRouter()

# Actions endpoints
@router.get("/actions", response_model=List[EditorGenerativeActionResponse])
def get_actions(
    workspace_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Récupérer toutes les actions génératives d'un workspace"""
    workspace = db.query(Workspace).filter(
        Workspace.id == workspace_id,
        Workspace.owner_id == current_user.id
    ).first()
    
    if not workspace:
        raise HTTPException(status_code=404, detail="Workspace non trouvé")
    
    actions = db.query(EditorGenerativeAction).filter(
        EditorGenerativeAction.workspace_id == workspace_id
    ).all()
    
    return actions

@router.post("/actions", response_model=EditorGenerativeActionResponse)
def create_action(
    action: EditorGenerativeActionCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Créer une nouvelle action générative"""
    workspace = db.query(Workspace).filter(
        Workspace.id == action.workspace_id,
        Workspace.owner_id == current_user.id
    ).first()
    
    if not workspace:
        raise HTTPException(status_code=404, detail="Workspace non trouvé")
    
    # Vérifier que l'action_key est unique dans le workspace
    existing = db.query(EditorGenerativeAction).filter(
        EditorGenerativeAction.workspace_id == action.workspace_id,
        EditorGenerativeAction.action_key == action.action_key
    ).first()
    
    if existing:
        raise HTTPException(status_code=400, detail="Cette action existe déjà")
    
    db_action = EditorGenerativeAction(**action.dict())
    db.add(db_action)
    db.commit()
    db.refresh(db_action)
    
    return db_action

@router.get("/actions/{action_id}", response_model=EditorGenerativeActionDetail)
def get_action_detail(
    action_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Récupérer une action avec ses prompts"""
    action = db.query(EditorGenerativeAction).filter(
        EditorGenerativeAction.id == action_id
    ).first()
    
    if not action:
        raise HTTPException(status_code=404, detail="Action non trouvée")
    
    # Vérifier l'accès au workspace
    workspace = db.query(Workspace).filter(
        Workspace.id == action.workspace_id,
        Workspace.owner_id == current_user.id
    ).first()
    
    if not workspace:
        raise HTTPException(status_code=403, detail="Accès non autorisé")
    
    return action

@router.put("/actions/{action_id}", response_model=EditorGenerativeActionResponse)
def update_action(
    action_id: int,
    action_update: EditorGenerativeActionUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Mettre à jour une action"""
    action = db.query(EditorGenerativeAction).filter(
        EditorGenerativeAction.id == action_id
    ).first()
    
    if not action:
        raise HTTPException(status_code=404, detail="Action non trouvée")
    
    # Vérifier l'accès au workspace
    workspace = db.query(Workspace).filter(
        Workspace.id == action.workspace_id,
        Workspace.owner_id == current_user.id
    ).first()
    
    if not workspace:
        raise HTTPException(status_code=403, detail="Accès non autorisé")
    
    for field, value in action_update.dict(exclude_unset=True).items():
        setattr(action, field, value)
    
    db.commit()
    db.refresh(action)
    
    return action

# Prompts endpoints
@router.get("/prompts", response_model=List[EditorGenerativePromptResponse])
def get_prompts(
    workspace_id: int,
    action_id: Optional[int] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Récupérer les prompts d'un workspace"""
    workspace = db.query(Workspace).filter(
        Workspace.id == workspace_id,
        Workspace.owner_id == current_user.id
    ).first()
    
    if not workspace:
        raise HTTPException(status_code=404, detail="Workspace non trouvé")
    
    query = db.query(EditorGenerativePrompt).filter(
        EditorGenerativePrompt.workspace_id == workspace_id
    )
    
    if action_id:
        query = query.filter(EditorGenerativePrompt.action_id == action_id)
    
    return query.all()

@router.post("/prompts", response_model=EditorGenerativePromptResponse)
def create_prompt(
    prompt: EditorGenerativePromptCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Créer un nouveau prompt"""
    workspace = db.query(Workspace).filter(
        Workspace.id == prompt.workspace_id,
        Workspace.owner_id == current_user.id
    ).first()
    
    if not workspace:
        raise HTTPException(status_code=404, detail="Workspace non trouvé")
    
    # Vérifier que l'action existe et appartient au workspace
    action = db.query(EditorGenerativeAction).filter(
        EditorGenerativeAction.id == prompt.action_id,
        EditorGenerativeAction.workspace_id == prompt.workspace_id
    ).first()
    
    if not action:
        raise HTTPException(status_code=404, detail="Action non trouvée")
    
    db_prompt = EditorGenerativePrompt(**prompt.dict())
    db.add(db_prompt)
    db.commit()
    db.refresh(db_prompt)
    
    return db_prompt

# Variables endpoints
@router.get("/variables", response_model=List[EditorGenerativeVariableResponse])
def get_variables(
    workspace_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Récupérer toutes les variables d'un workspace"""
    workspace = db.query(Workspace).filter(
        Workspace.id == workspace_id,
        Workspace.owner_id == current_user.id
    ).first()
    
    if not workspace:
        raise HTTPException(status_code=404, detail="Workspace non trouvé")
    
    variables = db.query(EditorGenerativeVariable).filter(
        EditorGenerativeVariable.workspace_id == workspace_id
    ).all()
    
    return variables

@router.post("/variables", response_model=EditorGenerativeVariableResponse)
def create_variable(
    variable: EditorGenerativeVariableCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Créer une nouvelle variable"""
    workspace = db.query(Workspace).filter(
        Workspace.id == variable.workspace_id,
        Workspace.owner_id == current_user.id
    ).first()
    
    if not workspace:
        raise HTTPException(status_code=404, detail="Workspace non trouvé")
    
    # Vérifier que la variable_key est unique dans le workspace
    existing = db.query(EditorGenerativeVariable).filter(
        EditorGenerativeVariable.workspace_id == variable.workspace_id,
        EditorGenerativeVariable.variable_key == variable.variable_key
    ).first()
    
    if existing:
        raise HTTPException(status_code=400, detail="Cette variable existe déjà")
    
    db_variable = EditorGenerativeVariable(**variable.dict())
    db.add(db_variable)
    db.commit()
    db.refresh(db_variable)
    
    return db_variable

# Execution endpoint
@router.post("/execute", response_model=ExecuteGenerativeActionResponse)
async def execute_action(
    request: ExecuteGenerativeActionRequest,
    workspace_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Exécuter une action générative avec prompts personnalisés"""
    service = EditorGenerativeService(db)
    
    workspace = db.query(Workspace).filter(
        Workspace.id == workspace_id,
        Workspace.owner_id == current_user.id
    ).first()
    
    if not workspace:
        raise HTTPException(status_code=404, detail="Workspace non trouvé")
    
    return await service.execute_action(
        workspace_id=workspace_id,
        action_key=request.action_key,
        variables=request.variables,
        context_data=request.context_data
    )
