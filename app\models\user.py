# app/models/user.py
from sqlalchemy import Column, String, DateTime, Boolean
from sqlalchemy.sql import func
from app.db.session import Base

class User(Base):
    """
    Modèle utilisateur - utilisé uniquement pour les relations
    Les données utilisateur sont stockées dans Supabase Auth
    """
    __tablename__ = "users"
    
    id = Column(String(50), primary_key=True, index=True)  # Supabase UUID
    email = Column(String(255), unique=True, index=True, nullable=False)
    full_name = Column(String(255))
    avatar_url = Column(String(500))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
