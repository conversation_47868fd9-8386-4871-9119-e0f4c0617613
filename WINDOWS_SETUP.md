# Windows Development Setup Guide

## 🪟 Windows Development Environment

Since you're developing on Windows but deploying to Linux (Render), here's your complete setup guide.

## 📦 Prerequisites for Windows

### 1. Install Python 3.11+
Download from: https://www.python.org/downloads/windows/
- Make sure to check "Add Python to PATH"
- Install to `C:\Python311` or similar

### 2. Install PostgreSQL
Download from: https://www.postgresql.org/download/windows/
- Default installation with pgAdmin
- Remember your postgres password

### 3. Redis for Windows Options

#### Option A: Redis for Windows (Recommended)
Download from: https://github.com/tporadowski/redis/releases
- Install Redis-x64-********.msi
- Redis will run on port 6379

#### Option B: Use Supabase Redis (Production)
For development, you can skip local Redis and use:
- Supabase Redis (when deployed)
- Or disable Redis in development

#### Option C: Docker Desktop (If Available)
Install Docker Desktop for Windows to use the provided docker-compose.yml

