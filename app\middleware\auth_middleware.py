# app/middleware/auth_middleware.py
from fastapi import HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from app.services.auth_service import auth_service
from app.db.session import get_db

security = HTTPBearer()

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Middleware pour récupérer l'utilisateur actuel"""
    token = credentials.credentials
    user = await auth_service.verify_token(token)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user

def require_workspace_access(workspace_id: int):
    """Décorateur pour vérifier l'accès à un workspace"""
    def workspace_access_checker(
        current_user: dict = Depends(get_current_user),
        db: Session = Depends(get_db)
    ):
        async def check():
            has_access = await auth_service.check_permission(
                current_user["id"], workspace_id, "workspace.read", db
            )
            if not has_access:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied to this workspace"
                )
            return {"user": current_user, "workspace_id": workspace_id}
        return check()
    return workspace_access_checker

def require_permission(permission: str, workspace_id_param: str = "workspace_id"):
    """Décorateur pour vérifier une permission spécifique"""
    def permission_checker(
        current_user: dict = Depends(get_current_user),
        db: Session = Depends(get_db)
    ):
        async def check(**kwargs):
            workspace_id = kwargs.get(workspace_id_param)
            if not workspace_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Workspace ID required"
                )
            
            has_permission = await auth_service.check_permission(
                current_user["id"], workspace_id, permission, db
            )
            if not has_permission:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Missing permission: {permission}"
                )
            return {"user": current_user, "workspace_id": workspace_id}
        return check()
    return permission_checker

def get_current_workspace_id(workspace_id: int):
    """Récupérer l'ID du workspace courant avec vérification"""
    return require_workspace_access(workspace_id)
