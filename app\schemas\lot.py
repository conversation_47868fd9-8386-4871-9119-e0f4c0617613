# app/schemas/lot.py
from pydantic import BaseModel, <PERSON>
from typing import Optional
from datetime import datetime

class LotBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    phase: Optional[str] = Field(None, max_length=50)
    order_number: Optional[int] = Field(None, ge=1)
    budget: Optional[int] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None

class LotCreate(LotBase):
    project_id: int

class LotUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    phase: Optional[str] = Field(None, max_length=50)
    order_number: Optional[int] = Field(None, ge=1)
    budget: Optional[int] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None

class LotResponse(LotBase):
    id: int
    project_id: int
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True
