# app/api/v1/endpoints/stakeholders.py
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from app.db.session import get_db
from app.models.lot import Stakeholder
from app.schemas.stakeholder import StakeholderCreate, StakeholderUpdate, StakeholderResponse
from app.middleware.auth_middleware import get_current_user

router = APIRouter()

@router.post("/", response_model=StakeholderResponse)
async def create_stakeholder(
    stakeholder: StakeholderCreate,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Créer un nouvel intervenant"""
    from app.models.lot import Lot
    from app.models.project import Project
    from app.models.rbac import WorkspaceUserRole
    
    # Vérifier l'accès au lot via le projet
    lot = db.query(Lot).join(
        Project,
        Lot.project_id == Project.id
    ).join(
        WorkspaceUserRole,
        Project.workspace_id == WorkspaceUserRole.workspace_id
    ).filter(
        Lot.id == stakeholder.lot_id,
        WorkspaceUserRole.user_id == current_user["id"]
    ).first()
    
    if not lot:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this lot"
        )
    
    db_stakeholder = Stakeholder(**stakeholder.dict())
    db.add(db_stakeholder)
    db.commit()
    db.refresh(db_stakeholder)
    
    return db_stakeholder

@router.get("/", response_model=List[StakeholderResponse])
async def get_stakeholders(
    lot_id: int = Query(..., description="ID du lot"),
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Récupérer tous les intervenants d'un lot"""
    from app.models.lot import Lot
    from app.models.project import Project
    from app.models.rbac import WorkspaceUserRole
    
    # Vérifier l'accès au lot
    lot = db.query(Lot).join(
        Project,
        Lot.project_id == Project.id
    ).join(
        WorkspaceUserRole,
        Project.workspace_id == WorkspaceUserRole.workspace_id
    ).filter(
        Lot.id == lot_id,
        WorkspaceUserRole.user_id == current_user["id"]
    ).first()
    
    if not lot:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this lot"
        )
    
    stakeholders = db.query(Stakeholder).filter(
        Stakeholder.lot_id == lot_id,
        Stakeholder.is_active == True
    ).all()
    
    return stakeholders

@router.get("/{stakeholder_id}", response_model=StakeholderResponse)
async def get_stakeholder(
    stakeholder_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Récupérer un intervenant spécifique"""
    from app.models.lot import Lot
    from app.models.project import Project
    from app.models.rbac import WorkspaceUserRole
    
    stakeholder = db.query(Stakeholder).join(
        Lot,
        Stakeholder.lot_id == Lot.id
    ).join(
        Project,
        Lot.project_id == Project.id
    ).join(
        WorkspaceUserRole,
        Project.workspace_id == WorkspaceUserRole.workspace_id
    ).filter(
        Stakeholder.id == stakeholder_id,
        WorkspaceUserRole.user_id == current_user["id"],
        Stakeholder.is_active == True
    ).first()
    
    if not stakeholder:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Stakeholder not found"
        )
    
    return stakeholder

@router.put("/{stakeholder_id}", response_model=StakeholderResponse)
async def update_stakeholder(
    stakeholder_id: int,
    stakeholder: StakeholderUpdate,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Mettre à jour un intervenant"""
    from app.models.lot import Lot
    from app.models.project import Project
    from app.models.rbac import WorkspaceUserRole
    
    db_stakeholder = db.query(Stakeholder).join(
        Lot,
        Stakeholder.lot_id == Lot.id
    ).join(
        Project,
        Lot.project_id == Project.id
    ).join(
        WorkspaceUserRole,
        Project.workspace_id == WorkspaceUserRole.workspace_id
    ).filter(
        Stakeholder.id == stakeholder_id,
        WorkspaceUserRole.user_id == current_user["id"]
    ).first()
    
    if not db_stakeholder:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Stakeholder not found"
        )
    
    for field, value in stakeholder.dict(exclude_unset=True).items():
        setattr(db_stakeholder, field, value)
    
    db.commit()
    db.refresh(db_stakeholder)
    
    return db_stakeholder

@router.delete("/{stakeholder_id}")
async def delete_stakeholder(
    stakeholder_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Supprimer un intervenant (soft delete)"""
    from app.models.lot import Lot
    from app.models.project import Project
    from app.models.rbac import WorkspaceUserRole
    
    db_stakeholder = db.query(Stakeholder).join(
        Lot,
        Stakeholder.lot_id == Lot.id
    ).join(
        Project,
        Lot.project_id == Project.id
    ).join(
        WorkspaceUserRole,
        Project.workspace_id == WorkspaceUserRole.workspace_id
    ).filter(
        Stakeholder.id == stakeholder_id,
        WorkspaceUserRole.user_id == current_user["id"]
    ).first()
    
    if not db_stakeholder:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Stakeholder not found"
        )
    
    db_stakeholder.is_active = False
    db.commit()
    
    return {"message": "Stakeholder deleted successfully"}
