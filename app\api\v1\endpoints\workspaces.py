# app/api/v1/endpoints/workspaces.py
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from app.db.session import get_db
from app.models.workspace import Workspace, WorkspaceSettings
from app.schemas.workspace import WorkspaceCreate, WorkspaceUpdate, WorkspaceResponse, WorkspaceSettingsUpdate, WorkspaceSettingsResponse
from app.middleware.auth_middleware import get_current_user, require_permission

router = APIRouter()

@router.post("/", response_model=WorkspaceResponse)
async def create_workspace(
    workspace: WorkspaceCreate,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Créer un nouveau workspace"""
    db_workspace = Workspace(**workspace.dict())
    db.add(db_workspace)
    db.commit()
    db.refresh(db_workspace)
    
    # Créer les settings par défaut
    settings = WorkspaceSettings(workspace_id=db_workspace.id)
    db.add(settings)
    db.commit()
    
    return db_workspace

@router.get("/", response_model=List[WorkspaceResponse])
async def get_workspaces(
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Récupérer tous les workspaces accessibles à l'utilisateur"""
    from app.models.rbac import WorkspaceUserRole
    
    workspaces = db.query(Workspace).join(
        WorkspaceUserRole,
        Workspace.id == WorkspaceUserRole.workspace_id
    ).filter(
        WorkspaceUserRole.user_id == current_user["id"],
        Workspace.is_active == True
    ).all()
    
    return workspaces

@router.get("/{workspace_id}", response_model=WorkspaceResponse)
async def get_workspace(
    workspace_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Récupérer un workspace spécifique"""
    from app.models.rbac import WorkspaceUserRole
    
    workspace = db.query(Workspace).join(
        WorkspaceUserRole,
        Workspace.id == WorkspaceUserRole.workspace_id
    ).filter(
        Workspace.id == workspace_id,
        WorkspaceUserRole.user_id == current_user["id"],
        Workspace.is_active == True
    ).first()
    
    if not workspace:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workspace not found"
        )
    
    return workspace

@router.put("/{workspace_id}", response_model=WorkspaceResponse)
async def update_workspace(
    workspace_id: int,
    workspace: WorkspaceUpdate,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Mettre à jour un workspace"""
    from app.models.rbac import WorkspaceUserRole
    
    db_workspace = db.query(Workspace).join(
        WorkspaceUserRole,
        Workspace.id == WorkspaceUserRole.workspace_id
    ).filter(
        Workspace.id == workspace_id,
        WorkspaceUserRole.user_id == current_user["id"],
        WorkspaceUserRole.role_name == "admin"
    ).first()
    
    if not db_workspace:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only workspace admins can update workspace"
        )
    
    for field, value in workspace.dict(exclude_unset=True).items():
        setattr(db_workspace, field, value)
    
    db.commit()
    db.refresh(db_workspace)
    
    return db_workspace

@router.delete("/{workspace_id}")
async def delete_workspace(
    workspace_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Supprimer un workspace (soft delete)"""
    from app.models.rbac import WorkspaceUserRole
    
    db_workspace = db.query(Workspace).join(
        WorkspaceUserRole,
        Workspace.id == WorkspaceUserRole.workspace_id
    ).filter(
        Workspace.id == workspace_id,
        WorkspaceUserRole.user_id == current_user["id"],
        WorkspaceUserRole.role_name == "admin"
    ).first()
    
    if not db_workspace:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only workspace admins can delete workspace"
        )
    
    db_workspace.is_active = False
    db.commit()
    
    return {"message": "Workspace deleted successfully"}

@router.get("/{workspace_id}/settings", response_model=WorkspaceSettingsResponse)
async def get_workspace_settings(
    workspace_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Récupérer les paramètres d'un workspace"""
    from app.models.rbac import WorkspaceUserRole
    
    settings = db.query(WorkspaceSettings).join(
        WorkspaceUserRole,
        WorkspaceSettings.workspace_id == WorkspaceUserRole.workspace_id
    ).filter(
        WorkspaceSettings.workspace_id == workspace_id,
        WorkspaceUserRole.user_id == current_user["id"]
    ).first()
    
    if not settings:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workspace settings not found"
        )
    
    return settings

@router.put("/{workspace_id}/settings", response_model=WorkspaceSettingsResponse)
async def update_workspace_settings(
    workspace_id: int,
    settings: WorkspaceSettingsUpdate,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Mettre à jour les paramètres d'un workspace"""
    from app.models.rbac import WorkspaceUserRole
    
    db_settings = db.query(WorkspaceSettings).join(
        WorkspaceUserRole,
        WorkspaceSettings.workspace_id == WorkspaceUserRole.workspace_id
    ).filter(
        WorkspaceSettings.workspace_id == workspace_id,
        WorkspaceUserRole.user_id == current_user["id"],
        WorkspaceUserRole.role_name == "admin"
    ).first()
    
    if not db_settings:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only workspace admins can update settings"
        )
    
    for field, value in settings.dict(exclude_unset=True).items():
        setattr(db_settings, field, value)
    
    db.commit()
    db.refresh(db_settings)
    
    return db_settings
