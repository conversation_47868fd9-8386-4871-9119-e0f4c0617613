# ORBIS Backend V2 - Deployment Guide

## 🚀 Render.com Deployment

### Prerequisites
- Render.com account
- Git repository with your code
- Supabase project
- OpenAI API key

### Step 1: Environment Variables Setup

1. **Copy production environment template:**
   ```bash
   cp .env.production.example .env.production
   ```

2. **Update the following variables in Render dashboard:**
   - `SUPABASE_URL`: Your Supabase project URL
   - `SUPABASE_ANON_KEY`: Your Supabase anon key
   - `SUPABASE_SERVICE_ROLE_KEY`: Your Supabase service role key
   - `OPENAI_API_KEY`: Your OpenAI API key

### Step 2: Deploy to Render

#### Option A: Automatic Deployment (Recommended)
1. Connect your GitHub repository to Render
2. Use the provided `render.yaml` file
3. Render will automatically:
   - Create PostgreSQL database
   - Create Redis instance
   - Deploy the application
   - Run migrations

#### Option B: Manual Deployment
1. Create a new Web Service on Render
2. Select your repository
3. Use `Dockerfile.render` as the Dockerfile
4. Set environment variables in Render dashboard
5. Deploy

### Step 3: Database Migration

The migrations will run automatically on deployment. To run manually:

```bash
alembic upgrade head
```

### Step 4: Health Check

After deployment, verify your service is running:
- Health endpoint: `https://your-app.onrender.com/health`
- API documentation: `https://your-app.onrender.com/docs`

## 🐳 Local Development with Docker

### Using Docker Compose (Recommended)

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f api

# Stop services
docker-compose down
```

### Using Docker only

```bash
# Build image
docker build -t orbis-backend-v2 .

# Run container
docker run -p 8000:8000 \
  -e DATABASE_URL=postgresql://user:pass@localhost:5432/orbis_v2 \
  -e REDIS_URL=redis://localhost:6379 \
  orbis-backend-v2
```

## 🔧 Environment Variables

### Required Variables
- `DATABASE_URL`: PostgreSQL connection string
- `ASYNC_DATABASE_URL`: Async PostgreSQL connection string
- `SUPABASE_URL`: Supabase project URL
- `SUPABASE_ANON_KEY`: Supabase anon key
- `SUPABASE_SERVICE_ROLE_KEY`: Supabase service role key
- `OPENAI_API_KEY`: OpenAI API key

### Optional Variables
- `SECRET_KEY`: JWT secret key (auto-generated if not provided)
- `ENVIRONMENT`: Set to 'production' for production
- `DEBUG`: Set to 'false' for production

## 📊 Monitoring

### Health Checks
- **Endpoint**: `/health`
- **Response**: `{"status": "healthy", "version": "2.0.0"}`

### Logs
- Application logs are available in Render dashboard
- Use `docker-compose logs -f api` for local development

## 🔄 CI/CD Pipeline

### GitHub Actions (Optional)
Create `.github/workflows/deploy.yml` for automated deployment on push to main branch.

### Manual Deployment
1. Push changes to your repository
2. Render will automatically deploy (if auto-deploy enabled)
3. Monitor deployment in Render dashboard

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Issues**
   - Verify DATABASE_URL format
   - Check if database is accessible

2. **Migration Failures**
   - Check alembic configuration
   - Verify database permissions

3. **Port Issues**
   - Ensure PORT environment variable is set (Render provides this)
   - Default port is 8000 for local development

### Debug Commands

```bash
# Check container logs
docker-compose logs api

# Access container shell
docker-compose exec api /bin/bash

# Check database connection
docker-compose exec postgres psql -U postgres -d orbis_v2
```

## 📞 Support

For deployment issues:
1. Check Render logs
2. Verify environment variables
3. Test locally with Docker
4. Check Supabase and OpenAI configurations