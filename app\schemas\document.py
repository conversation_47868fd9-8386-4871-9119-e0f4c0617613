# app/schemas/document.py
from pydantic import BaseModel, <PERSON>
from typing import Optional

class DocumentBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    file_path: Optional[str] = None
    file_size: Optional[int] = None
    mime_type: Optional[str] = Field(None, max_length=100)

class DocumentCreate(DocumentBase):
    workspace_id: int

class DocumentUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None

class DocumentResponse(DocumentBase):
    id: int
    workspace_id: int
    is_active: bool
    created_at: str
    updated_at: Optional[str] = None
    
    class Config:
        from_attributes = True
