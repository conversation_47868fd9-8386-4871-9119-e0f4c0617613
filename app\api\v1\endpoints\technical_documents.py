# app/api/v1/endpoints/technical_documents.py
from fastapi import APIRouter, Depends, HTTPException, status, Query, UploadFile, File
from sqlalchemy.orm import Session
from typing import List, Optional
from app.db.session import get_db
from app.models.document import TechnicalDocument
from app.schemas.technical_document import TechnicalDocumentCreate, TechnicalDocumentUpdate, TechnicalDocumentResponse
from app.middleware.auth_middleware import get_current_user

router = APIRouter()

@router.post("/", response_model=TechnicalDocumentResponse)
async def create_technical_document(
    document: TechnicalDocumentCreate,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Créer un nouveau document technique"""
    from app.models.project import Project
    from app.models.rbac import WorkspaceUserRole
    
    # Vérifier l'accès au projet via le workspace
    project = db.query(Project).join(
        WorkspaceUserRole,
        Project.workspace_id == WorkspaceUserRole.workspace_id
    ).filter(
        Project.id == document.project_id,
        WorkspaceUserRole.user_id == current_user["id"]
    ).first()
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this project"
        )
    
    db_document = TechnicalDocument(**document.dict())
    db.add(db_document)
    db.commit()
    db.refresh(db_document)
    
    return db_document

@router.get("/", response_model=List[TechnicalDocumentResponse])
async def get_technical_documents(
    workspace_id: int = Query(..., description="ID du workspace"),
    project_id: Optional[int] = Query(None, description="ID du projet"),
    lot_id: Optional[int] = Query(None, description="ID du lot"),
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Récupérer tous les documents techniques d'un workspace"""
    from app.models.project import Project
    from app.models.rbac import WorkspaceUserRole
    
    # Vérifier l'accès au workspace
    access = db.query(WorkspaceUserRole).filter(
        WorkspaceUserRole.workspace_id == workspace_id,
        WorkspaceUserRole.user_id == current_user["id"]
    ).first()
    
    if not access:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this workspace"
        )
    
    query = db.query(TechnicalDocument).join(
        Project,
        TechnicalDocument.project_id == Project.id
    ).filter(
        Project.workspace_id == workspace_id,
        TechnicalDocument.is_active == True
    )
    
    if project_id:
        query = query.filter(TechnicalDocument.project_id == project_id)
    
    if lot_id:
        query = query.filter(TechnicalDocument.lot_id == lot_id)
    
    documents = query.all()
    
    return documents

@router.get("/{document_id}", response_model=TechnicalDocumentResponse)
async def get_technical_document(
    document_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Récupérer un document technique spécifique"""
    from app.models.project import Project
    from app.models.rbac import WorkspaceUserRole
    
    document = db.query(TechnicalDocument).join(
        Project,
        TechnicalDocument.project_id == Project.id
    ).join(
        WorkspaceUserRole,
        Project.workspace_id == WorkspaceUserRole.workspace_id
    ).filter(
        TechnicalDocument.id == document_id,
        WorkspaceUserRole.user_id == current_user["id"],
        TechnicalDocument.is_active == True
    ).first()
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Technical document not found"
        )
    
    return document

@router.put("/{document_id}", response_model=TechnicalDocumentResponse)
async def update_technical_document(
    document_id: int,
    document: TechnicalDocumentUpdate,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Mettre à jour un document technique"""
    from app.models.project import Project
    from app.models.rbac import WorkspaceUserRole
    
    db_document = db.query(TechnicalDocument).join(
        Project,
        TechnicalDocument.project_id == Project.id
    ).join(
        WorkspaceUserRole,
        Project.workspace_id == WorkspaceUserRole.workspace_id
    ).filter(
        TechnicalDocument.id == document_id,
        WorkspaceUserRole.user_id == current_user["id"]
    ).first()
    
    if not db_document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Technical document not found"
        )
    
    for field, value in document.dict(exclude_unset=True).items():
        setattr(db_document, field, value)
    
    db.commit()
    db.refresh(db_document)
    
    return db_document

@router.delete("/{document_id}")
async def delete_technical_document(
    document_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Supprimer un document technique (soft delete)"""
    from app.models.project import Project
    from app.models.rbac import WorkspaceUserRole
    
    db_document = db.query(TechnicalDocument).join(
        Project,
        TechnicalDocument.project_id == Project.id
    ).join(
        WorkspaceUserRole,
        Project.workspace_id == WorkspaceUserRole.workspace_id
    ).filter(
        TechnicalDocument.id == document_id,
        WorkspaceUserRole.user_id == current_user["id"]
    ).first()
    
    if not db_document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Technical document not found"
        )
    
    db_document.is_active = False
    db.commit()
    
    return {"message": "Technical document deleted successfully"}

@router.post("/{document_id}/generate-article")
async def generate_article(
    document_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Générer un article CCTP avec ChatGPT"""
    from app.models.project import Project
    from app.models.rbac import WorkspaceUserRole
    from app.services.chatgpt_service import ChatGPTService
    
    document = db.query(TechnicalDocument).join(
        Project,
        TechnicalDocument.project_id == Project.id
    ).join(
        WorkspaceUserRole,
        Project.workspace_id == WorkspaceUserRole.workspace_id
    ).filter(
        TechnicalDocument.id == document_id,
        WorkspaceUserRole.user_id == current_user["id"]
    ).first()
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Technical document not found"
        )
    
    # Ici vous implémenteriez l'appel à ChatGPT
    # Pour l'instant, on retourne une réponse simulée
    return {
        "generated_content": "Article généré avec succès",
        "document_id": document_id
    }

@router.post("/{document_id}/enhance-text")
async def enhance_text(
    document_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Améliorer le texte avec ChatGPT"""
    from app.models.project import Project
    from app.models.rbac import WorkspaceUserRole
    from app.services.chatgpt_service import ChatGPTService
    
    document = db.query(TechnicalDocument).join(
        Project,
        TechnicalDocument.project_id == Project.id
    ).join(
        WorkspaceUserRole,
        Project.workspace_id == WorkspaceUserRole.workspace_id
    ).filter(
        TechnicalDocument.id == document_id,
        WorkspaceUserRole.user_id == current_user["id"]
    ).first()
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Technical document not found"
        )
    
    # Ici vous implémenteriez l'appel à ChatGPT
    # Pour l'instant, on retourne une réponse simulée
    return {
        "enhanced_content": "Texte amélioré avec succès",
        "document_id": document_id
    }
