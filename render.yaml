services:
  # Main API service
  - type: web
    name: orbis-backend-v2
    env: docker
    dockerfilePath: ./Dockerfile.render
    dockerContext: .
    envVars:
      - key: DATABASE_URL
        fromDatabase:
          name: orbis-postgres
          property: connectionString
      - key: ASYNC_DATABASE_URL
        fromDatabase:
          name: orbis-postgres
          property: connectionString
        value: postgresql+asyncpg://${DATABASE_URL}
      - key: REDIS_URL
        fromService:
          type: redis
          name: orbis-redis
          property: connectionString
      - key: SUPABASE_URL
        sync: false
      - key: SUPABASE_ANON_KEY
        sync: false
      - key: SUPABASE_SERVICE_ROLE_KEY
        sync: false
      - key: SECRET_KEY
        generateValue: true
      - key: ENVIRONMENT
        value: production
      - key: DEBUG
        value: false
      - key: API_HOST
        value: 0.0.0.0
      - key: API_PORT
        value: 8000
    healthCheckPath: /health
    buildCommand: pip install -r requirements.txt
    startCommand: uvicorn app.main:app --host 0.0.0.0 --port $PORT

  # Redis for caching
  - type: redis
    name: orbis-redis
    ipAllowList: []
    plan: starter

databases:
  # PostgreSQL database
  - name: orbis-postgres
    databaseName: orbis_v2
    user: orbis_user
    plan: starter