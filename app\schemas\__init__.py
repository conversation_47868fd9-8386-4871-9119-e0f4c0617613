# app/schemas/__init__.py
from app.schemas.user import UserResponse, UserCreate, UserUpdate
from app.schemas.workspace import WorkspaceResponse, WorkspaceCreate, WorkspaceUpdate
from app.schemas.project import ProjectResponse, ProjectCreate, ProjectUpdate
from app.schemas.lot import LotResponse, LotCreate, LotUpdate
from app.schemas.stakeholder import StakeholderResponse, StakeholderCreate, StakeholderUpdate
from app.schemas.tcompany import TCompanyResponse, TCompanyCreate, TCompanyUpdate
from app.schemas.document import DocumentResponse, DocumentCreate, DocumentUpdate
from app.schemas.technical_document import TechnicalDocumentResponse, TechnicalDocumentCreate, TechnicalDocumentUpdate
from app.schemas.rbac import RoleResponse, PermissionResponse

__all__ = [
    "UserResponse", "UserCreate", "UserUpdate",
    "WorkspaceResponse", "WorkspaceCreate", "WorkspaceUpdate",
    "ProjectResponse", "ProjectCreate", "ProjectUpdate",
    "LotResponse", "LotCreate", "LotUpdate",
    "StakeholderResponse", "StakeholderCreate", "StakeholderUpdate",
    "TCompanyResponse", "TCompanyCreate", "TCompanyUpdate",
    "DocumentResponse", "DocumentCreate", "DocumentUpdate",
    "TechnicalDocumentResponse", "TechnicalDocumentCreate", "TechnicalDocumentUpdate",
    "RoleResponse", "PermissionResponse"
]
