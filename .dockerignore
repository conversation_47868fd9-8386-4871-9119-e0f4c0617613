# Git
.git
.gitignore
.gitattributes

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
.venv
pip-log.txt
pip-delete-this-directory.txt

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Documentation
docs/
*.md
!README.md

# Docker
Dockerfile
docker-compose*.yml
.dockerignore

# Environment files
.env
.env.local
.env.*.local

# Logs
*.log
logs/

# Uploads (keep the directory structure but ignore files)
uploads/*
!uploads/.gitkeep

# Database
*.db
*.sqlite3

# Temporary files
tmp/
temp/
*.tmp