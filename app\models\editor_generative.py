# app/models/editor_generative.py
from sqlalchemy import Column, Integer, String, Text, Boolean, ForeignKey, DateTime, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.session import Base

class EditorGenerativeAction(Base):
    __tablename__ = "editor_generative_actions"
    
    id = Column(Integer, primary_key=True, index=True)
    workspace_id = Column(Integer, ForeignKey("workspaces.id", ondelete="CASCADE"), nullable=False, index=True)
    action_key = Column(String(100), nullable=False, index=True)  # ex: "addArticleCCTP"
    label = Column(String(255), nullable=False)  # ex: "Ajouter un article de CCTP"
    description = Column(Text)
    category = Column(String(100), default="general")  # ex: "cctp", "lot", "document"
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    workspace = relationship("Workspace")
    prompts = relationship("EditorGenerativePrompt", back_populates="action", cascade="all, delete-orphan")

class EditorGenerativePrompt(Base):
    __tablename__ = "editor_generative_prompts"
    
    id = Column(Integer, primary_key=True, index=True)
    workspace_id = Column(Integer, ForeignKey("workspaces.id", ondelete="CASCADE"), nullable=False, index=True)
    action_id = Column(Integer, ForeignKey("editor_generative_actions.id", ondelete="CASCADE"), nullable=False, index=True)
    prompt_template = Column(Text, nullable=False)
    model = Column(String(100), default="gpt-4o")
    max_tokens = Column(Integer, default=2000)
    temperature = Column(String(10), default="0.7")
    is_active = Column(Boolean, default=True)
    is_default = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    workspace = relationship("Workspace")
    action = relationship("EditorGenerativeAction", back_populates="prompts")
    variables = relationship("EditorGenerativeVariable", secondary="editor_generative_prompt_variables", back_populates="prompts")

class EditorGenerativeVariable(Base):
    __tablename__ = "editor_generative_variables"
    
    id = Column(Integer, primary_key=True, index=True)
    workspace_id = Column(Integer, ForeignKey("workspaces.id", ondelete="CASCADE"), nullable=False, index=True)
    variable_key = Column(String(100), nullable=False, index=True)  # ex: "prestation"
    variable_name = Column(String(255), nullable=False)  # ex: "Prestation à réaliser"
    variable_type = Column(String(50), nullable=False)  # string, number, boolean, object, array
    description = Column(Text)
    example_value = Column(Text)
    validation_rules = Column(JSON)  # {"required": true, "min_length": 1, "max_length": 500}
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    workspace = relationship("Workspace")
    prompts = relationship("EditorGenerativePrompt", secondary="editor_generative_prompt_variables", back_populates="variables")

class EditorGenerativePromptVariable(Base):
    __tablename__ = "editor_generative_prompt_variables"
    
    id = Column(Integer, primary_key=True, index=True)
    prompt_id = Column(Integer, ForeignKey("editor_generative_prompts.id", ondelete="CASCADE"), nullable=False, index=True)
    variable_id = Column(Integer, ForeignKey("editor_generative_variables.id", ondelete="CASCADE"), nullable=False, index=True)
    is_required = Column(Boolean, default=True)
    default_value = Column(Text)
    order_index = Column(Integer, default=0)
    
    # Constraints
    __table_args__ = (
        {'sqlite_autoincrement': True},
    )
