# app/api/v1/endpoints/lots.py
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from app.db.session import get_db
from app.models.lot import Lot
from app.schemas.lot import LotCreate, LotUpdate, LotResponse
from app.middleware.auth_middleware import get_current_user

router = APIRouter()

@router.post("/", response_model=LotResponse)
async def create_lot(
    lot: LotCreate,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Créer un nouveau lot"""
    from app.models.project import Project
    from app.models.rbac import WorkspaceUserRole
    
    # Vérifier l'accès au projet via le workspace
    project = db.query(Project).join(
        WorkspaceUserRole,
        Project.workspace_id == WorkspaceUserRole.workspace_id
    ).filter(
        Project.id == lot.project_id,
        WorkspaceUserRole.user_id == current_user["id"]
    ).first()
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this project"
        )
    
    db_lot = Lot(**lot.dict())
    db.add(db_lot)
    db.commit()
    db.refresh(db_lot)
    
    return db_lot

@router.get("/", response_model=List[LotResponse])
async def get_lots(
    project_id: int = Query(..., description="ID du projet"),
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Récupérer tous les lots d'un projet"""
    from app.models.project import Project
    from app.models.rbac import WorkspaceUserRole
    
    # Vérifier l'accès au projet
    project = db.query(Project).join(
        WorkspaceUserRole,
        Project.workspace_id == WorkspaceUserRole.workspace_id
    ).filter(
        Project.id == project_id,
        WorkspaceUserRole.user_id == current_user["id"]
    ).first()
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this project"
        )
    
    lots = db.query(Lot).filter(
        Lot.project_id == project_id,
        Lot.is_active == True
    ).all()
    
    return lots

@router.get("/{lot_id}", response_model=LotResponse)
async def get_lot(
    lot_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Récupérer un lot spécifique"""
    from app.models.project import Project
    from app.models.rbac import WorkspaceUserRole
    
    lot = db.query(Lot).join(
        Project,
        Lot.project_id == Project.id
    ).join(
        WorkspaceUserRole,
        Project.workspace_id == WorkspaceUserRole.workspace_id
    ).filter(
        Lot.id == lot_id,
        WorkspaceUserRole.user_id == current_user["id"],
        Lot.is_active == True
    ).first()
    
    if not lot:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Lot not found"
        )
    
    return lot

@router.put("/{lot_id}", response_model=LotResponse)
async def update_lot(
    lot_id: int,
    lot: LotUpdate,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Mettre à jour un lot"""
    from app.models.project import Project
    from app.models.rbac import WorkspaceUserRole
    
    db_lot = db.query(Lot).join(
        Project,
        Lot.project_id == Project.id
    ).join(
        WorkspaceUserRole,
        Project.workspace_id == WorkspaceUserRole.workspace_id
    ).filter(
        Lot.id == lot_id,
        WorkspaceUserRole.user_id == current_user["id"]
    ).first()
    
    if not db_lot:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Lot not found"
        )
    
    for field, value in lot.dict(exclude_unset=True).items():
        setattr(db_lot, field, value)
    
    db.commit()
    db.refresh(db_lot)
    
    return db_lot

@router.delete("/{lot_id}")
async def delete_lot(
    lot_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Supprimer un lot (soft delete)"""
    from app.models.project import Project
    from app.models.rbac import WorkspaceUserRole
    
    db_lot = db.query(Lot).join(
        Project,
        Lot.project_id == Project.id
    ).join(
        WorkspaceUserRole,
        Project.workspace_id == WorkspaceUserRole.workspace_id
    ).filter(
        Lot.id == lot_id,
        WorkspaceUserRole.user_id == current_user["id"]
    ).first()
    
    if not db_lot:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Lot not found"
        )
    
    db_lot.is_active = False
    db.commit()
    
    return {"message": "Lot deleted successfully"}
