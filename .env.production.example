# Production Environment Configuration for Render.com
# Copy this file to .env.production and update with your actual values

# Database - Will be provided by Render
DATABASE_URL=postgresql://username:password@host:port/database
ASYNC_DATABASE_URL=postgresql+asyncpg://username:password@host:port/database

# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Security
SECRET_KEY=your-super-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=3600

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME="ORBIS Suivi Travaux Production"
API_HOST=0.0.0.0
API_PORT=8000

# CORS Configuration for Production
BACKEND_CORS_ORIGINS_PROD=https://your-frontend-domain.com,https://your-admin-domain.com

# Environment
ENVIRONMENT=production
DEBUG=false

# File Upload
UPLOAD_FOLDER=uploads
MAX_FILE_SIZE=10485760

# OpenAI Configuration
OPENAI_API_KEY=your-openai-key
OPENAI_MODEL=gpt-4o

# Redis - Will be provided by Render
REDIS_URL=redis://default:password@host:port