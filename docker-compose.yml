version: '3.8'

services:
  # PostgreSQL Database (for local development)
  postgres:
    image: postgres:15-alpine
    container_name: orbis-postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: orbis_v2
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: orbis-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # ORBIS Backend API
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: orbis-api
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=********************************************/orbis_v2
      - ASYNC_DATABASE_URL=postgresql+asyncpg://postgres:postgres@postgres:5432/orbis_v2
      - REDIS_URL=redis://redis:6379
      - ENVIRONMENT=development
      - DEBUG=true
    volumes:
      - ./uploads:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

  # Nginx Reverse Proxy (for production-like setup)
  nginx:
    image: nginx:alpine
    container_name: orbis-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - api
    restart: unless-stopped
    profiles:
      - production

volumes:
  postgres_data:
  redis_data:

networks:
  default:
    name: orbis-network